'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import authService, { User, LoginRequest, SignupRequest } from '@/lib/auth-service';
import { CookieUtils } from '@/lib/cookies';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (data: LoginRequest) => Promise<void>;
  signup: (data: SignupRequest) => Promise<void>;
  googleLogin: (idToken: string) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('AuthContext: Initializing authentication...');

        // Check cookies for authentication
        const token = CookieUtils.get('accessToken');
        const userCookieStr = CookieUtils.get('user');

        console.log('AuthContext: Cookie check:', {
          hasToken: !!token,
          hasUserCookie: !!userCookieStr,
          tokenLength: token?.length || 0,
          userCookieLength: userCookieStr?.length || 0
        });

        if (token && userCookieStr) {
          try {
            // Parse user data from cookie
            const userData = JSON.parse(decodeURIComponent(userCookieStr));
            console.log('AuthContext: Parsed user data from cookie:', userData);

            setUser(userData);
            setIsAuthenticated(true);
            console.log('AuthContext: Authentication successful from cookies');

            // Optional: Verify token is still valid by fetching current user
            // Commented out to avoid unnecessary API calls during initialization
            /*
            try {
              const currentUser = await authService.getCurrentUser();
              setUser(currentUser);
              console.log('AuthContext: User data refreshed from API');
            } catch (error) {
              console.warn('AuthContext: Token verification failed, but keeping cookie auth:', error);
            }
            */
          } catch (parseError) {
            console.error('AuthContext: Error parsing user cookie:', parseError);
            console.error('AuthContext: Raw user cookie:', userCookieStr);
            // Clear invalid cookies
            authService.logout();
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          console.log('AuthContext: No valid authentication cookies found');
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('AuthContext: Auth initialization error:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
        console.log('AuthContext: Initialization complete');
      }
    };

    initAuth();
  }, []);

  const login = async (data: LoginRequest) => {
    try {
      setLoading(true);
      const response = await authService.login(data);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (data: SignupRequest) => {
    try {
      setLoading(true);
      const response = await authService.signup(data);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const googleLogin = async (idToken: string) => {
    try {
      setLoading(true);
      const response = await authService.googleLogin(idToken);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    router.push('/auth/login');
  };

  const refreshUser = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, logout user
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    loading,
    login,
    signup,
    googleLogin,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
