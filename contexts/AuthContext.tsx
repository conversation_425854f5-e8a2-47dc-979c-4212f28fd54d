'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import authService, { User, LoginRequest, SignupRequest } from '@/lib/auth-service';
import { CookieUtils } from '@/lib/cookies';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (data: LoginRequest) => Promise<void>;
  signup: (data: SignupRequest) => Promise<void>;
  googleLogin: (idToken: string) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children, initialUser }: { children: ReactNode, initialUser?: User | null }) {
  const [user, setUser] = useState<User | null>(initialUser ?? null);
  const [isAuthenticated, setIsAuthenticated] = useState(!!initialUser);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('AuthContext: Initializing authentication...');
        console.log('AuthContext: Current pathname:', window.location.pathname);

        // Check all cookies for debugging
        const allCookies = document.cookie;
        console.log('AuthContext: All cookies:', allCookies);

        // Check cookies for authentication token
        const accessToken = CookieUtils.get('accessToken');
        const refreshToken = CookieUtils.get('refreshToken');
        const userCookie = CookieUtils.get('user');

        console.log('AuthContext: Cookie check:', {
          hasAccessToken: !!accessToken,
          accessTokenLength: accessToken?.length || 0,
          hasRefreshToken: !!refreshToken,
          refreshTokenLength: refreshToken?.length || 0,
          hasUserCookie: !!userCookie,
          userCookieLength: userCookie?.length || 0
        });

        if (accessToken) {
          try {
            // Always fetch fresh user data from API instead of using cookie
            console.log('AuthContext: Token found, fetching user data from API...');
            const currentUser = await authService.getCurrentUser();
            console.log('AuthContext: User data from API:', currentUser);

            setUser(currentUser);
            setIsAuthenticated(true);
            console.log('AuthContext: Authentication successful from API');
          } catch (error) {
            console.error('AuthContext: API call failed, clearing auth:', error);
            console.error('AuthContext: Error details:', {
              message: error instanceof Error ? error.message : 'Unknown error',
              status: (error as any)?.response?.status,
              data: (error as any)?.response?.data
            });
            // Clear invalid tokens
            authService.logout();
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          console.log('AuthContext: No valid authentication cookies found');
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('AuthContext: Auth initialization error:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
        console.log('AuthContext: Initialization complete, final state:', {
          isAuthenticated: isAuthenticated,
          hasUser: !!user,
          loading: false
        });
      }
    };

    initAuth();
  }, []);

  const login = async (data: LoginRequest) => {
    try {
      setLoading(true);
      const response = await authService.login(data);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (data: SignupRequest) => {
    try {
      setLoading(true);
      const response = await authService.signup(data);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const googleLogin = async (idToken: string) => {
    try {
      setLoading(true);
      const response = await authService.googleLogin(idToken);
      console.log('Google login response user:', response.user);
      console.log('User imageUrl:', response.user.imageUrl);
      console.log('User roles:', response.user.roles);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    router.push('/auth/login');
  };

  const refreshUser = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, logout user
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    loading,
    login,
    signup,
    googleLogin,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
