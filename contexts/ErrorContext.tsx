'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ErrorResponse } from '@/lib/types/common';
import { ErrorNotification, NotificationType } from '@/components/shared/ErrorNotification';

interface ErrorNotificationItem {
  id: string;
  type: NotificationType;
  title?: string;
  message: string;
  error?: ErrorResponse;
  duration?: number;
  showRetry?: boolean;
  onRetry?: () => void;
}

interface ErrorContextType {
  notifications: ErrorNotificationItem[];
  showError: (message: string, options?: Partial<ErrorNotificationItem>) => string;
  showWarning: (message: string, options?: Partial<ErrorNotificationItem>) => string;
  showInfo: (message: string, options?: Partial<ErrorNotificationItem>) => string;
  showSuccess: (message: string, options?: Partial<ErrorNotificationItem>) => string;
  showApiError: (error: ErrorResponse, options?: Partial<ErrorNotificationItem>) => string;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
  maxNotifications?: number;
  defaultDuration?: number;
}

export const ErrorProvider: React.FC<ErrorProviderProps> = ({
  children,
  maxNotifications = 5,
  defaultDuration = 5000
}) => {
  const [notifications, setNotifications] = useState<ErrorNotificationItem[]>([]);

  const generateId = useCallback(() => {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const addNotification = useCallback((notification: Omit<ErrorNotificationItem, 'id'>) => {
    const id = generateId();
    const newNotification: ErrorNotificationItem = {
      ...notification,
      id,
      duration: notification.duration ?? defaultDuration
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      // Keep only the latest notifications
      return updated.slice(0, maxNotifications);
    });

    // Auto-remove notification after duration
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, [generateId, defaultDuration, maxNotifications]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const showError = useCallback((message: string, options?: Partial<ErrorNotificationItem>) => {
    return addNotification({
      type: 'error',
      message,
      ...options
    });
  }, [addNotification]);

  const showWarning = useCallback((message: string, options?: Partial<ErrorNotificationItem>) => {
    return addNotification({
      type: 'warning',
      message,
      ...options
    });
  }, [addNotification]);

  const showInfo = useCallback((message: string, options?: Partial<ErrorNotificationItem>) => {
    return addNotification({
      type: 'info',
      message,
      ...options
    });
  }, [addNotification]);

  const showSuccess = useCallback((message: string, options?: Partial<ErrorNotificationItem>) => {
    return addNotification({
      type: 'success',
      message,
      ...options
    });
  }, [addNotification]);

  const showApiError = useCallback((error: ErrorResponse, options?: Partial<ErrorNotificationItem>) => {
    return addNotification({
      type: 'error',
      message: error.message,
      error,
      ...options
    });
  }, [addNotification]);

  const value: ErrorContextType = {
    notifications,
    showError,
    showWarning,
    showInfo,
    showSuccess,
    showApiError,
    removeNotification,
    clearAll
  };

  return (
    <ErrorContext.Provider value={value}>
      {children}
      
      {/* Render notifications */}
      <div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
        {notifications.map((notification) => (
          <ErrorNotification
            key={notification.id}
            type={notification.type}
            title={notification.title}
            message={notification.message}
            error={notification.error}
            showRetry={notification.showRetry}
            onRetry={notification.onRetry}
            onClose={() => removeNotification(notification.id)}
            className="shadow-lg"
          />
        ))}
      </div>
    </ErrorContext.Provider>
  );
};

export const useErrorContext = () => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useErrorContext must be used within an ErrorProvider');
  }
  return context;
};

// Combined hook that provides both error handling and notifications
export const useErrorNotification = () => {
  const errorContext = useErrorContext();
  
  const handleError = useCallback((error: unknown, options?: {
    showNotification?: boolean;
    customMessage?: string;
    showRetry?: boolean;
    onRetry?: () => void;
  }) => {
    const { showNotification = true, customMessage, showRetry, onRetry } = options || {};
    
    let errorResponse: ErrorResponse;
    
    // Parse error
    if (error && typeof error === 'object' && 'response' in error) {
      // Axios error
      const axiosError = error as any;
      errorResponse = axiosError.response?.data || {
        status: axiosError.response?.status || 500,
        error: 'Unknown Error',
        message: axiosError.message || 'An error occurred',
        timestamp: new Date().toISOString()
      };
    } else if (error instanceof Error) {
      errorResponse = {
        status: 500,
        error: 'Internal Error',
        message: error.message,
        timestamp: new Date().toISOString()
      };
    } else {
      errorResponse = {
        status: 500,
        error: 'Unknown Error',
        message: customMessage || 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      };
    }
    
    if (showNotification) {
      errorContext.showApiError(errorResponse, {
        message: customMessage || errorResponse.message,
        showRetry,
        onRetry
      });
    }
    
    return errorResponse;
  }, [errorContext]);
  
  return {
    ...errorContext,
    handleError
  };
};
