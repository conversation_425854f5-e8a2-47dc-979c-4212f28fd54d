// Backend ile uyumlu ElementType enum
export enum ElementType {
  TEXT = 'TEXT',           // Single line text input
  TEXTAREA = 'TEXTAREA',   // Multi-line text input
  NUMBER = 'NUMBER',       // Numeric input
  EMAIL = 'EMAIL',         // Email input
  PASSWORD = 'PASSWORD',   // Password input
  DATE = 'DATE',           // Date picker
  TIME = 'TIME',           // Time picker
  DATETIME = 'DATETIME',   // Date and time picker
  CHECKBOX = 'CHECKBOX',   // Single checkbox
  CHECKBOX_GROUP = 'CHECKBOX_GROUP', // Multiple checkboxes
  RADIO_GROUP = 'RADIO_GROUP',       // Radio buttons
  SELECT = 'SELECT',       // Dropdown select
  MULTI_SELECT = 'MULTI_SELECT',     // Multi-select dropdown
  FILE = 'FILE',           // File upload
  RANGE = 'RANGE',         // Range slider
  HIDDEN = 'HIDDEN',       // Hidden field
  PHONE = 'PHONE',         // Phone number
  URL = 'URL',             // URL input
  COLOR = 'COLOR',         // Color picker
  RATING = 'RATING'        // Star rating
}

// Backend CreateFormTemplateRequest ile uyumlu
export interface CreateFormTemplateRequest {
  nameTranslations: Record<string, string>;
  categoryId: number;
  descriptionTranslations?: Record<string, string>;
  elements: CreateFormElementRequest[];
  active?: boolean;
}

// Backend CreateFormElementRequest ile uyumlu
export interface CreateFormElementRequest {
  labelTranslations: Record<string, string>;
  descriptionTranslations?: Record<string, string>;
  placeholderTranslations?: Record<string, string>;
  helpTextTranslations?: Record<string, string>;
  type: ElementType;
  required: boolean;
  displayOrder: number;
  options?: CreateElementOptionRequest[];
  validationRegex?: string;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  properties?: Record<string, string>;
}

// Backend CreateElementOptionRequest ile uyumlu
export interface CreateElementOptionRequest {
  value: string;
  labelTranslations: Record<string, string>;
  displayOrder: number;
  defaultSelected?: boolean;
  propertyTranslations?: Record<string, Record<string, string>>;
}

// Frontend için form element interface
export interface FormElement {
  id: string;
  type: ElementType;
  labelTranslations: Record<string, string>;
  descriptionTranslations?: Record<string, string>;
  placeholderTranslations?: Record<string, string>;
  helpTextTranslations?: Record<string, string>;
  required: boolean;
  displayOrder: number;
  options?: ElementOption[];
  validationRegex?: string;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  properties?: Record<string, string>;
}

// Frontend için element option interface
export interface ElementOption {
  id: string;
  labelTranslations: Record<string, string>;
  value: string;
  displayOrder: number;
  defaultSelected?: boolean;
}

// Category interface for simple categories endpoint
export interface Category {
  id: number;
  name: string;
  parentId?: number;
  type: string;
  level: number;
  fullPath: string;
  active: boolean;
}

// Full category interface with translations (for other endpoints)
export interface FullCategory {
  id: number;
  nameTranslations: Record<string, string>;
  descriptionTranslations?: Record<string, string>;
  parentId?: number;
  level: number;
  categoryType: string;
  active: boolean;
}

// Form template interface for backend response
export interface FormTemplate {
  id: number;
  nameTranslations: Record<string, string>;
  descriptionTranslations?: Record<string, string>;
  categoryId: number;
  elements: FormElement[];
  active: boolean;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}
