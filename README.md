# LookForX Admin Panel

A modern admin panel built with Next.js, TypeScript, and Tailwind CSS for managing the LookForX microservices platform.

## Features

- 🔐 **Authentication**: Google OAuth integration with JWT tokens
- 👥 **User Management**: List, edit, activate/deactivate users
- 🛡️ **Role Management**: Assign and manage user roles (ADMIN, MODERATOR, USER)
- 📊 **Dashboard**: Overview of system statistics
- 🎨 **Modern UI**: Built with shadcn/ui and Tailwind CSS
- 📱 **Responsive**: Mobile-friendly design
- 🔒 **Secure**: Cookie-based authentication with proper CORS

## Tech Stack

- **Framework**: [Next.js](https://nextjs.org/) 15.3.4
- **UI Library**: [shadcn/ui](https://ui.shadcn.com/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Icons**: [Lucide React](https://lucide.dev/)
- **HTTP Client**: [Axios](https://axios-http.com/)
- **Language**: TypeScript

## Project Structure

```
lookforx-admin-panel-webapp/
├── app/
│   ├── auth/
│   │   ├── login/page.tsx          # Login page
│   │   ├── signup/page.tsx         # Signup page
│   │   └── callback/google/page.tsx # Google OAuth callback
│   ├── dashboard/page.tsx          # Admin dashboard
│   ├── admin/page.tsx             # Admin landing page
│   └── layout.tsx                 # Root layout
├── components/
│   ├── ui/                        # shadcn/ui components
│   └── GoogleLoginButton.tsx      # Google OAuth button
├── contexts/
│   └── AuthContext.tsx            # Authentication context
├── lib/
│   ├── api.ts                     # API client configuration
│   ├── auth-service.ts            # Auth service methods
│   └── utils.ts                   # Utility functions
└── .env.local                     # Environment variables
```

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
