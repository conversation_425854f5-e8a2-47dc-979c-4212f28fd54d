import api from './api';
import {
  Category,
  CategoryType,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryListResponse,
  SearchRequest,
  SearchResponse,
  CategorySearchDocument,
  SearchMode,
  LanguageCode
} from './types/category';

// Simple category response for form dropdowns
export interface SimpleCategoryResponse {
  id: number;
  name: string;
  type: CategoryType;
  level: number;
}

class CategoryService {
  private readonly CATEGORY_BASE_URL = '/category-service/api/categories';
  private readonly SEARCH_BASE_URL = '/search-service/api/v1/categories';

  // Get all categories
  async getAllCategories(): Promise<Category[]> {
    const response = await api.get<Category[]>(this.CATEGORY_BASE_URL);
    return response.data;
  }

  // Get all categories as flat list (no hierarchical structure)
  async getAllCategoriesFlat(): Promise<Category[]> {
    try {
      const response = await api.get<Category[]>(`${this.CATEGORY_BASE_URL}?flat=true`);
      return response.data;
    } catch (error) {
      console.error('Error fetching flat categories:', error);
      // Fallback: get all and flatten manually
      const hierarchical = await this.getAllCategories();
      return this.flattenCategories(hierarchical);
    }
  }

  // Get simple categories for form dropdowns (English names only)
  async getSimpleCategories(type?: CategoryType, search?: string): Promise<SimpleCategoryResponse[]> {
    try {
      const params = new URLSearchParams();
      params.append('language', LanguageCode.EN); // Always use English for parent selection

      if (type) {
        params.append('type', type);
      }
      if (search) {
        params.append('search', search);
      }

      const url = `${this.CATEGORY_BASE_URL}/simple?${params.toString()}`;
      console.log('CategoryService: getSimpleCategories URL:', url);

      const response = await api.get<SimpleCategoryResponse[]>(url);
      console.log('CategoryService: getSimpleCategories response:', response.data);
      return response.data;
    } catch (error) {
      console.error('CategoryService: Error fetching simple categories:', error);
      throw error;
    }
  }

  // Get root categories by type (for tab display)
  async getRootCategoriesByType(type: CategoryType): Promise<Category[]> {
    try {
      const response = await api.get<Category[]>(`${this.CATEGORY_BASE_URL}/root?type=${type}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching root categories:', error);
      throw error;
    }
  }

  // Helper method to flatten hierarchical categories
  private flattenCategories(categories: Category[]): Category[] {
    const result: Category[] = [];
    const flatten = (cats: Category[]) => {
      cats.forEach(cat => {
        // Create a copy without subcategories to avoid circular references
        const flatCat: Category = {
          id: cat.id,
          parentId: cat.parentId,
          translations: cat.translations,
          type: cat.type,
          level: cat.level,
          createdBy: cat.createdBy,
          createdAt: cat.createdAt,
          revisedAt: cat.revisedAt
        };
        result.push(flatCat);

        if (cat.subcategories && cat.subcategories.length > 0) {
          flatten(cat.subcategories);
        }
      });
    };
    flatten(categories);
    return result;
  }

  // Get categories by parent and type
  async getCategoriesByParentAndType(parentId?: number, type?: CategoryType): Promise<Category[]> {
    try {
      const params = new URLSearchParams();
      if (parentId !== undefined) {
        params.append('parentId', parentId.toString());
      }
      if (type) {
        params.append('type', type);
      }

      const response = await api.get<Category[]>(`${this.CATEGORY_BASE_URL}/by-parent-and-type?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching categories by parent and type:', error);
      throw error;
    }
  }

  // Get category by ID
  async getCategoryById(id: number): Promise<Category> {
    try {
      const response = await api.get<Category>(`${this.CATEGORY_BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category:', error);
      throw error;
    }
  }

  // Create new category
  async createCategory(request: CreateCategoryRequest): Promise<Category> {
    try {
      const response = await api.post<Category>(this.CATEGORY_BASE_URL, request);
      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id: number, request: UpdateCategoryRequest): Promise<Category> {
    try {
      const response = await api.put<Category>(`${this.CATEGORY_BASE_URL}/${id}`, request);
      return response.data;
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  async deleteCategory(id: number): Promise<void> {
    try {
      await api.delete(`${this.CATEGORY_BASE_URL}/${id}`);
    } catch (error: any) {
      console.error('Error deleting category:', error);

      // Re-throw with additional context for better error handling
      if (error.response?.status === 409) {
        const enhancedError = new Error('Category has subcategories and cannot be deleted');
        (enhancedError as any).response = error.response;
        throw enhancedError;
      } else if (error.response?.status === 404) {
        const enhancedError = new Error('Category not found');
        (enhancedError as any).response = error.response;
        throw enhancedError;
      }

      throw error;
    }
  }

  // Clear category cache
  async clearCache(): Promise<void> {
    try {
      await api.post(`${this.CATEGORY_BASE_URL}/clear-cache`);
    } catch (error) {
      console.error('Error clearing category cache:', error);
      throw error;
    }
  }

  // Clear category cache
  async clearCache(): Promise<void> {
    try {
      await api.post(`${this.CATEGORY_BASE_URL}/clear-cache`);
    } catch (error) {
      console.error('Error clearing category cache:', error);
      throw error;
    }
  }

  // Search categories using search service
  async searchCategories(request: SearchRequest): Promise<CategorySearchDocument[]> {
    try {
      console.log('CategoryService: Searching with request:', request);

      const params = new URLSearchParams({
        query: request.query,
        mode: request.mode || SearchMode.FULL_TEXT,
        limit: (request.limit || 50).toString()
      });

      // Add language parameter if specified
      if (request.language && request.language !== 'ALL') {
        params.append('language', request.language);
      }

      const url = `${this.SEARCH_BASE_URL}/search?${params.toString()}`;
      console.log('CategoryService: Search URL:', url);

      const response = await api.get<CategorySearchDocument[]>(url);
      console.log('CategoryService: Search response:', response.data);
      return response.data;
    } catch (error) {
      console.error('CategoryService: Error searching categories:', error);
      throw error;
    }
  }

  // Get root categories (no parent) by type
  async getRootCategories(type: CategoryType): Promise<Category[]> {
    try {
      return await this.getCategoriesByParentAndType(undefined, type);
    } catch (error) {
      console.error('Error fetching root categories:', error);
      throw error;
    }
  }

  // Get subcategories of a parent
  async getSubcategories(parentId: number, type: CategoryType): Promise<Category[]> {
    try {
      return await this.getCategoriesByParentAndType(parentId, type);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      throw error;
    }
  }

  // Helper method to parse translations JSON
  parseTranslationsJson(translationsJson: string): Record<string, string> {
    try {
      const parsed = JSON.parse(translationsJson);
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error('Translations must be an object');
      }
      return parsed;
    } catch (error) {
      throw new Error('Invalid JSON format for translations');
    }
  }

  // Helper method to validate translations
  validateTranslations(translations: Record<string, string>): string[] {
    const errors: string[] = [];
    
    if (!translations.EN) {
      errors.push('English (EN) translation is required');
    }
    
    // Check if all values are strings
    for (const [key, value] of Object.entries(translations)) {
      if (typeof value !== 'string' || value.trim() === '') {
        errors.push(`Translation for ${key} must be a non-empty string`);
      }
    }
    
    return errors;
  }

  // Helper method to calculate level based on parent
  async calculateLevel(parentId?: number): Promise<number> {
    if (!parentId) {
      return 1; // Root level
    }
    
    try {
      const parent = await this.getCategoryById(parentId);
      return parent.level + 1;
    } catch (error) {
      console.error('Error calculating level:', error);
      return 1; // Default to root level if parent not found
    }
  }

  // Helper method to get category display name (English translation)
  getCategoryDisplayName(category: Category): string {
    // Try both uppercase and lowercase keys for backward compatibility
    return category.translations.EN || category.translations.en ||
           category.translations[Object.keys(category.translations)[0]] || 'Unnamed Category';
  }

  // Helper method to get category display name with Turkish subtitle
  getCategoryDisplayNameWithTurkish(category: Category): { primary: string; secondary?: string } {
    // Try both uppercase and lowercase keys for backward compatibility
    const primary = category.translations.EN || category.translations.en ||
                   category.translations[Object.keys(category.translations)[0]] || 'Unnamed Category';
    const secondary = (category.translations.TR || category.translations.tr) &&
                     (category.translations.TR || category.translations.tr) !== primary ?
                     (category.translations.TR || category.translations.tr) : undefined;
    return { primary, secondary };
  }

  // Helper method to format category for dropdown
  formatCategoryForDropdown(category: Category): { value: number; label: string; level: number } {
    const displayName = this.getCategoryDisplayName(category);
    const indent = '  '.repeat(category.level - 1);
    return {
      value: category.id,
      label: `${indent}${displayName}`,
      level: category.level
    };
  }


}

export const categoryService = new CategoryService();
export default categoryService;
