import { AxiosError } from 'axios';
import { ErrorResponse } from '@/lib/types/common';

/**
 * Utility functions for error handling across the admin panel
 */

// Extract user-friendly error message from various error types
export const extractErrorMessage = (error: unknown, fallback = 'An unexpected error occurred'): string => {
  if (error instanceof AxiosError) {
    // Backend error response
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    
    // HTTP status based messages
    switch (error.response?.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'You are not authorized to perform this action.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 409:
        return 'This action conflicts with existing data.';
      case 422:
        return 'The provided data is invalid.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
        return 'Service temporarily unavailable. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return error.message || fallback;
    }
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return fallback;
};

// Convert various error types to standardized ErrorResponse
export const normalizeError = (error: unknown): ErrorResponse => {
  if (error instanceof AxiosError && error.response?.data) {
    // Backend error response - already in correct format
    const backendError = error.response.data as ErrorResponse;
    return {
      status: backendError.status || error.response.status,
      error: backendError.error || error.response.statusText,
      message: backendError.message || extractErrorMessage(error),
      errorCode: backendError.errorCode,
      path: backendError.path,
      timestamp: backendError.timestamp || new Date().toISOString(),
      fieldErrors: backendError.fieldErrors,
      traceId: backendError.traceId
    };
  }
  
  if (error instanceof AxiosError) {
    // Network or request error
    return {
      status: error.response?.status || 0,
      error: error.response?.statusText || 'Network Error',
      message: extractErrorMessage(error),
      timestamp: new Date().toISOString()
    };
  }
  
  if (error instanceof Error) {
    // JavaScript error
    return {
      status: 500,
      error: 'Internal Error',
      message: error.message,
      timestamp: new Date().toISOString()
    };
  }
  
  // Unknown error
  return {
    status: 500,
    error: 'Unknown Error',
    message: typeof error === 'string' ? error : 'An unexpected error occurred',
    timestamp: new Date().toISOString()
  };
};

// Check if error is a specific type
export const isNetworkError = (error: unknown): boolean => {
  return error instanceof AxiosError && !error.response;
};

export const isValidationError = (error: unknown): boolean => {
  return error instanceof AxiosError && 
         error.response?.status === 400 && 
         (error.response?.data?.errorCode === 'VALIDATION_ERROR' || 
          error.response?.data?.fieldErrors);
};

export const isUnauthorizedError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 401;
};

export const isForbiddenError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 403;
};

export const isNotFoundError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 404;
};

export const isConflictError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 409;
};

export const isServerError = (error: unknown): boolean => {
  return error instanceof AxiosError && 
         error.response?.status && 
         error.response.status >= 500;
};

// Get appropriate retry strategy based on error type
export const shouldRetry = (error: unknown): boolean => {
  if (isNetworkError(error)) return true;
  if (isServerError(error)) return true;
  if (error instanceof AxiosError && error.response?.status === 429) return true; // Rate limit
  return false;
};

// Get user-friendly error title based on error type
export const getErrorTitle = (error: unknown): string => {
  if (isValidationError(error)) return 'Validation Error';
  if (isUnauthorizedError(error)) return 'Authentication Required';
  if (isForbiddenError(error)) return 'Access Denied';
  if (isNotFoundError(error)) return 'Not Found';
  if (isConflictError(error)) return 'Conflict';
  if (isNetworkError(error)) return 'Network Error';
  if (isServerError(error)) return 'Server Error';
  return 'Error';
};

// Format field errors for display
export const formatFieldErrors = (fieldErrors?: Record<string, string>): string => {
  if (!fieldErrors || Object.keys(fieldErrors).length === 0) {
    return '';
  }
  
  const errors = Object.entries(fieldErrors)
    .map(([field, message]) => `${field}: ${message}`)
    .join(', ');
    
  return `Field errors: ${errors}`;
};

// Create error message with context
export const createContextualErrorMessage = (
  operation: string,
  error: unknown,
  context?: string
): string => {
  const baseMessage = extractErrorMessage(error);
  const contextPrefix = context ? `${context}: ` : '';
  const operationPrefix = `Failed to ${operation}`;
  
  if (baseMessage.toLowerCase().includes(operation.toLowerCase())) {
    return `${contextPrefix}${baseMessage}`;
  }
  
  return `${contextPrefix}${operationPrefix}. ${baseMessage}`;
};

// Common error messages for different operations
export const ERROR_MESSAGES = {
  LOAD: 'Failed to load data',
  SAVE: 'Failed to save changes',
  DELETE: 'Failed to delete item',
  UPDATE: 'Failed to update item',
  CREATE: 'Failed to create item',
  SEARCH: 'Failed to search',
  UPLOAD: 'Failed to upload file',
  DOWNLOAD: 'Failed to download file',
  NETWORK: 'Network connection error',
  PERMISSION: 'You do not have permission to perform this action',
  VALIDATION: 'Please check your input and try again',
  SERVER: 'Server error. Please try again later',
  UNKNOWN: 'An unexpected error occurred'
} as const;
