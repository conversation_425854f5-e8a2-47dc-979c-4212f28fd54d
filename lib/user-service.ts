import api from './api';

// Types
export interface User {
  id: string;
  name: string;
  email: string;
  imageUrl?: string;
  roles: string[];
  emailVerified?: boolean;
  active: boolean;
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
}

export interface UserListResponse {
  users: User[];
  totalCount: number;
  page: number;
  size: number;
  totalPages: number;
}

export interface UpdateUserRoleRequest {
  userId: string;
  roles: string[];
}

export interface UpdateUserStatusRequest {
  userId: string;
  active: boolean;
}

export interface UserSearchParams {
  page?: number;
  size?: number;
  search?: string;
  role?: string;
  active?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

class UserService {
  private readonly USER_BASE_URL = '/auth-service/api/v1/admin/users';

  // Get all users with pagination and filters
  async getUsers(params: UserSearchParams = {}): Promise<UserListResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page !== undefined) queryParams.append('page', params.page.toString());
      if (params.size !== undefined) queryParams.append('size', params.size.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.role) queryParams.append('role', params.role);
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);

      const response = await api.get<UserListResponse>(`${this.USER_BASE_URL}?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User> {
    try {
      const response = await api.get<User>(`${this.USER_BASE_URL}/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  // Update user roles
  async updateUserRoles(userId: string, roles: string[]): Promise<User> {
    try {
      const response = await api.put<User>(`${this.USER_BASE_URL}/${userId}/roles`, { roles });
      return response.data;
    } catch (error) {
      console.error('Error updating user roles:', error);
      throw error;
    }
  }

  // Update user status (activate/deactivate)
  async updateUserStatus(userId: string, active: boolean): Promise<User> {
    try {
      const response = await api.put<User>(`${this.USER_BASE_URL}/${userId}/status`, { active });
      return response.data;
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  }

  // Delete user (soft delete)
  async deleteUser(userId: string): Promise<void> {
    try {
      await api.delete(`${this.USER_BASE_URL}/${userId}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Get available roles
  async getAvailableRoles(): Promise<string[]> {
    try {
      const response = await api.get<string[]>(`${this.USER_BASE_URL}/roles`);
      return response.data;
    } catch (error) {
      console.error('Error fetching roles:', error);
      // Fallback roles
      return ['USER', 'ADMIN', 'MODERATOR'];
    }
  }

  // Get user statistics
  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    adminUsers: number;
    regularUsers: number;
  }> {
    try {
      const response = await api.get(`${this.USER_BASE_URL}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
export default userService;
