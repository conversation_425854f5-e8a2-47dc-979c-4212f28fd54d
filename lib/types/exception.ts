import { LanguageCode } from './common';

// HTTP Status codes enum
export enum HttpStatus {
  OK = 'OK',
  CREATED = 'CREATED',
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

// Exception entity interface
export interface ExceptionEntity {
  id: number;
  exceptionCode: string;
  messages: Record<LanguageCode, string>;
  httpStatus: HttpStatus;
  createdAt?: string;
  updatedAt?: string;
}

// Request DTOs
export interface CreateExceptionRequest {
  exceptionCode: string;
  messages: Record<LanguageCode, string>;
  httpStatus: HttpStatus;
}

export interface UpdateExceptionRequest {
  exceptionCode?: string;
  messages?: Record<LanguageCode, string>;
  httpStatus?: HttpStatus;
}

// Response DTOs
export interface ExceptionResponse {
  id: number;
  exceptionCode: string;
  messages: Record<LanguageCode, string>;
  httpStatus: HttpStatus;
  createdAt?: string;
  updatedAt?: string;
}

export interface ExceptionMessageResponse {
  exceptionCode: string;
  languageCode: LanguageCode;
  message: string;
}

// Form data interface
export interface ExceptionFormData {
  exceptionCode: string;
  messagesJson: string;
  httpStatus: HttpStatus;
}

// HTTP Status options for UI
export const HTTP_STATUS_OPTIONS = [
  { value: HttpStatus.OK, label: '200 - OK', description: 'Request successful' },
  { value: HttpStatus.CREATED, label: '201 - Created', description: 'Resource created successfully' },
  { value: HttpStatus.BAD_REQUEST, label: '400 - Bad Request', description: 'Invalid request data' },
  { value: HttpStatus.UNAUTHORIZED, label: '401 - Unauthorized', description: 'Authentication required' },
  { value: HttpStatus.FORBIDDEN, label: '403 - Forbidden', description: 'Access denied' },
  { value: HttpStatus.NOT_FOUND, label: '404 - Not Found', description: 'Resource not found' },
  { value: HttpStatus.CONFLICT, label: '409 - Conflict', description: 'Resource conflict' },
  { value: HttpStatus.INTERNAL_SERVER_ERROR, label: '500 - Internal Server Error', description: 'Server error' },
  { value: HttpStatus.SERVICE_UNAVAILABLE, label: '503 - Service Unavailable', description: 'Service temporarily unavailable' }
];

// Common exception codes for reference
export const COMMON_EXCEPTION_CODES = [
  'USER_NOT_FOUND',
  'INVALID_CREDENTIALS',
  'ACCESS_DENIED',
  'RESOURCE_NOT_FOUND',
  'VALIDATION_ERROR',
  'DUPLICATE_RESOURCE',
  'INTERNAL_ERROR',
  'SERVICE_UNAVAILABLE',
  'RATE_LIMIT_EXCEEDED',
  'INVALID_TOKEN',
  'EXPIRED_TOKEN',
  'INSUFFICIENT_PERMISSIONS',
  'INVALID_INPUT',
  'RESOURCE_LOCKED',
  'OPERATION_NOT_ALLOWED'
];
