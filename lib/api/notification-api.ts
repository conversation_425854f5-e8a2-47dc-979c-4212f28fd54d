const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

// Bulk Notification API (for admin panel dashboard)
export const bulkNotificationApi = {
  baseUrl: `${API_BASE_URL}/notification-service`,

  async getNotifications(params: {
    page?: number;
    size?: number;
    search?: string;
    notificationType?: string;
    status?: string;
    showInBell?: string;
    sortBy?: string;
    sortDirection?: string;
  } = {}): Promise<any> {
    const searchParams = new URLSearchParams();
    
    if (params.page !== undefined) searchParams.append('page', params.page.toString());
    if (params.size !== undefined) searchParams.append('size', params.size.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.notificationType && params.notificationType !== 'all') {
      searchParams.append('notificationType', params.notificationType);
    }
    if (params.status && params.status !== 'all') {
      searchParams.append('status', params.status);
    }
    if (params.showInBell && params.showInBell !== 'all') {
      searchParams.append('showInBell', params.showInBell);
    }
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortDirection) searchParams.append('sortDirection', params.sortDirection);

    const response = await fetch(`${this.baseUrl}/admin/notifications?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch notifications');
    }

    return response.json();
  },

  async getNotificationById(id: number): Promise<any> {
    const response = await fetch(`${this.baseUrl}/admin/notifications/${id}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch notification');
    }

    return response.json();
  },

  async updateNotification(id: number, data: any): Promise<any> {
    const response = await fetch(`${this.baseUrl}/admin/notifications/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to update notification');
    }

    return response.json();
  },

  async deleteNotification(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/admin/notifications/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete notification');
    }
  },

  async createNotification(data: any): Promise<any> {
    const response = await fetch(`${this.baseUrl}/admin/notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to create notification');
    }

    return response.json();
  },

  async createBulkNotification(data: any): Promise<any> {
    console.log('API: Creating bulk notification with data:', data);
    console.log('API: URL:', `${this.baseUrl}/api/admin/bulk-notifications`);

    const response = await fetch(`${this.baseUrl}/api/admin/bulk-notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    console.log('API: Response status:', response.status);
    console.log('API: Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API: Error response:', errorText);
      throw new Error(`Failed to create bulk notification: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log('API: Success result:', result);
    return result;
  },

  async getDashboardBulkNotifications(userId: string, page: number = 0, size: number = 10): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/admin/bulk-notifications/dashboard?userId=${userId}&page=${page}&size=${size}`);

    if (!response.ok) {
      throw new Error('Failed to fetch dashboard bulk notifications');
    }

    return response.json();
  },

  async markBulkNotificationAsRead(bulkNotificationId: number, userId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/admin/bulk-notifications/${bulkNotificationId}/read?userId=${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to mark bulk notification as read');
    }

    return response.json();
  },

  async markAllBulkNotificationsAsRead(userId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/admin/bulk-notifications/read-all?userId=${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to mark all bulk notifications as read');
    }

    return response.json();
  },

  async getBulkNotificationStats(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/admin/bulk-notifications/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch bulk notification statistics');
    }

    return response.json();
  },

  async getNotificationTypes(): Promise<string[]> {
    const response = await fetch(`${this.baseUrl}/admin/notifications/types`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch notification types');
    }

    return response.json();
  },

  async getNotificationStatistics(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/admin/notifications/statistics`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch notification statistics');
    }

    return response.json();
  },

};

// Admin Bell Notification API (for admin panel bell notifications via API Gateway)
export const adminBellNotificationApi = {
  baseUrl: `${API_BASE_URL}/notification-service`,

  async getUnreadCount(userId: string): Promise<{ success: boolean; count: number; userId: string; error?: string }> {
    const response = await fetch(`${this.baseUrl}/api/admin/bell-notifications/user/${userId}/count`);

    if (!response.ok) {
      throw new Error('Failed to fetch unread count');
    }

    return response.json();
  },

  async getUserNotifications(userId: string, page: number = 0, size: number = 20, language: string = 'EN'): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/admin/bell-notifications/user/${userId}?page=${page}&size=${size}&language=${language}`);

    if (!response.ok) {
      throw new Error('Failed to fetch user notifications');
    }

    return response.json();
  },

  async markNotificationAsRead(userId: string, notificationId: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const response = await fetch(`${this.baseUrl}/api/admin/bell-notifications/user/${userId}/notification/${notificationId}/read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to mark notification as read');
    }

    return response.json();
  },

  async markAllNotificationsAsRead(userId: string): Promise<{ success: boolean; updatedCount: number; message?: string; error?: string }> {
    const response = await fetch(`${this.baseUrl}/api/admin/bell-notifications/user/${userId}/mark-all-read`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error('Failed to mark all notifications as read');
    }

    return response.json();
  },
};

// Legacy export for backward compatibility
export const notificationApi = bulkNotificationApi;
