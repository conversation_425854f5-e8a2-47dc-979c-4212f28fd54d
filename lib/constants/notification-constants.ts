export const RELATED_ENTITY_TYPES = [
  { value: 'USER', label: 'User' },
  { value: 'CATEGORY', label: 'Category' },
  { value: 'PRODUCT', label: 'Product' },
  { value: 'ORDER', label: 'Order' },
  { value: 'PAYMENT', label: 'Payment' },
  { value: 'FORM', label: 'Form' },
  { value: 'SUBMISSION', label: 'Form Submission' },
  { value: 'EXCEPTION', label: 'Exception' },
  { value: 'SYSTEM', label: 'System' },
  { value: 'ANNOUNCEMENT', label: 'Announcement' },
  { value: 'PROMOTION', label: 'Promotion' },
  { value: 'MAINTENANCE', label: 'Maintenance' },
  { value: 'SECURITY', label: 'Security' },
  { value: 'NOTIFICATION', label: 'Notification' },
  { value: 'REPORT', label: 'Report' },
  { value: 'ANALYTICS', label: 'Analytics' },
  { value: 'FEEDBACK', label: 'Feedback' },
  { value: 'SUPPORT', label: 'Support' },
  { value: 'BILLING', label: 'Billing' },
  { value: 'SUBSCRIPTION', label: 'Subscription' },
] as const;

export const SUPPORTED_LANGUAGES = [
  { code: 'EN', name: 'English' },
  { code: 'TR', name: 'Turkish' },
  { code: 'DE', name: 'German' },
  { code: 'FR', name: 'French' },
  { code: 'ES', name: 'Spanish' },
  { code: 'ZH', name: 'Chinese' },
  { code: 'HI', name: 'Hindi' },
  { code: 'RU', name: 'Russian' },
  { code: 'AR', name: 'Arabic' },
  { code: 'PT', name: 'Portuguese' },
  { code: 'IT', name: 'Italian' },
  { code: 'JA', name: 'Japanese' },
  { code: 'KO', name: 'Korean' },
  { code: 'NL', name: 'Dutch' },
  { code: 'PL', name: 'Polish' },
  { code: 'DA', name: 'Danish' },
  { code: 'SV', name: 'Swedish' },
  { code: 'NO', name: 'Norwegian' },
  { code: 'FI', name: 'Finnish' },
  { code: 'CS', name: 'Czech' },
  { code: 'HU', name: 'Hungarian' },
  { code: 'RO', name: 'Romanian' },
  { code: 'EL', name: 'Greek' },
  { code: 'TH', name: 'Thai' },
  { code: 'VI', name: 'Vietnamese' },
  { code: 'ID', name: 'Indonesian' },
  { code: 'MS', name: 'Malay' },
  { code: 'HE', name: 'Hebrew' },
  { code: 'UR', name: 'Urdu' },
  { code: 'FA', name: 'Persian' },
  { code: 'BN', name: 'Bengali' },
  { code: 'PA', name: 'Punjabi' },
  { code: 'SQ', name: 'Albanian' },
  { code: 'BS', name: 'Bosnian' },
  { code: 'HR', name: 'Croatian' },
  { code: 'SK', name: 'Slovak' },
  { code: 'SL', name: 'Slovenian' },
  { code: 'LT', name: 'Lithuanian' },
  { code: 'LV', name: 'Latvian' },
  { code: 'ET', name: 'Estonian' },
  { code: 'BG', name: 'Bulgarian' },
  { code: 'MK', name: 'Macedonian' },
  { code: 'SR', name: 'Serbian' },
  { code: 'CY', name: 'Welsh' },
  { code: 'GA', name: 'Irish' },
  { code: 'IS', name: 'Icelandic' },
  { code: 'GL', name: 'Galician' },
  { code: 'KY', name: 'Kyrgyz' },
  { code: 'NE', name: 'Nepali' },
  { code: 'KM', name: 'Khmer' },
  { code: 'LA', name: 'Latin' },
  { code: 'MY', name: 'Myanmar' },
  { code: 'MN', name: 'Mongolian' },
  { code: 'TG', name: 'Tajik' },
  { code: 'UZ', name: 'Uzbek' },
  { code: 'AZ', name: 'Azerbaijani' },
  { code: 'HY', name: 'Armenian' },
  { code: 'BE', name: 'Belarusian' },
  { code: 'UK', name: 'Ukrainian' },
] as const;

export type RelatedEntityType = typeof RELATED_ENTITY_TYPES[number]['value'];
export type LanguageCode = typeof SUPPORTED_LANGUAGES[number]['code'];
