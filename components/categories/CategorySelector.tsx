'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import categoryService from '@/lib/category-service';
import { SearchMode } from '@/lib/types/category';

interface CategoryOption {
  id: number;
  name: string;
  fullPath?: string;
  type?: string;
  level?: number;
}

interface CategorySelectorProps {
  value?: number | null;
  onChange: (value: number | null) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  className?: string;
  disabled?: boolean;
  includeAllOption?: boolean;
  allOptionLabel?: string;
}

export function CategorySelector({
  value,
  onChange,
  placeholder = "Select category...",
  searchPlaceholder = "Search categories...",
  className,
  disabled = false,
  includeAllOption = true,
  allOptionLabel = "All Categories",
}: CategorySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Search categories when query changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setCategories([]);
      setHasSearched(false);
      return;
    }

    const debounceTimer = setTimeout(async () => {
      try {
        setLoading(true);
        setHasSearched(true);
        
        console.log('Searching categories with query:', searchQuery);
        
        const results = await categoryService.searchCategories({
          query: searchQuery.trim(),
          mode: SearchMode.FULL_TEXT,
          limit: 50
        });

        console.log('Category search results:', results);

        // Convert search results to CategoryOption format
        const categoryOptions: CategoryOption[] = results.map(result => ({
          id: parseInt(result.id),
          name: result.translations?.EN || result.translations?.en || 'Unknown',
          fullPath: `${result.translations?.EN || result.translations?.en || 'Unknown'} (Level ${result.level})`,
          type: result.type,
          level: result.level
        }));

        setCategories(categoryOptions);
      } catch (error) {
        console.error('Error searching categories:', error);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const selectedCategory = categories.find(cat => cat.id === value);
  
  // If we have a selected value but no category in our current list, 
  // we need to fetch it (this happens when component loads with a pre-selected value)
  useEffect(() => {
    if (value && !selectedCategory && !loading) {
      // Try to get the category name from simple categories endpoint
      const fetchSelectedCategory = async () => {
        try {
          const simpleCategories = await categoryService.getSimpleCategories();
          const found = simpleCategories.find(cat => cat.id === value);
          if (found) {
            const categoryOption: CategoryOption = {
              id: found.id,
              name: found.name,
              fullPath: found.fullPath,
              type: found.type,
              level: found.level
            };
            setCategories(prev => {
              // Only add if not already in the list
              if (!prev.find(cat => cat.id === value)) {
                return [categoryOption, ...prev];
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error fetching selected category:', error);
        }
      };
      
      fetchSelectedCategory();
    }
  }, [value, selectedCategory, loading]);

  const handleSelect = (option: CategoryOption | null) => {
    if (option) {
      onChange(option.id);
    } else {
      onChange(null);
    }
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
    setSearchQuery('');
  };

  const displayOptions = includeAllOption 
    ? [{ id: 0, name: allOptionLabel, fullPath: allOptionLabel }, ...categories]
    : categories;

  const displayValue = value === null || value === 0 
    ? (includeAllOption ? allOptionLabel : null)
    : selectedCategory?.name || `Category ID: ${value}`;

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      {/* Trigger */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          "placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
      >
        <span className={cn("truncate", !displayValue && "text-muted-foreground")}>
          {displayValue || placeholder}
        </span>
        <div className="flex items-center space-x-1">
          {value && !disabled && (
            <X
              className="h-4 w-4 text-muted-foreground hover:text-foreground"
              onClick={handleClear}
            />
          )}
          <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-popover shadow-md">
          {/* Search Input */}
          <div className="flex items-center border-b px-3 py-2">
            <Search className="h-4 w-4 text-muted-foreground mr-2" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 bg-transparent outline-none text-sm placeholder:text-muted-foreground"
            />
          </div>

          {/* Options */}
          <div className="max-h-60 overflow-auto p-1">
            {loading ? (
              <div className="px-3 py-2 text-sm text-muted-foreground">Searching...</div>
            ) : !hasSearched && !searchQuery ? (
              <div className="px-3 py-2 text-sm text-muted-foreground">
                Start typing to search categories...
              </div>
            ) : displayOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-muted-foreground">
                No categories found
              </div>
            ) : (
              displayOptions.map((option) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => handleSelect(option.id === 0 ? null : option)}
                  className={cn(
                    "w-full text-left px-3 py-2 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground",
                    "focus:bg-accent focus:text-accent-foreground focus:outline-none",
                    ((value === null || value === 0) && option.id === 0) || value === option.id 
                      ? "bg-accent text-accent-foreground" : ""
                  )}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{option.name}</span>
                    {option.fullPath && option.fullPath !== option.name && (
                      <span className="text-xs text-muted-foreground">{option.fullPath}</span>
                    )}
                    {option.type && option.id !== 0 && (
                      <span className="text-xs text-muted-foreground">Type: {option.type}</span>
                    )}
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
