'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import categoryService, { SimpleCategoryResponse } from '@/lib/category-service';
import {
  Category,
  CategoryType,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFormData,
  CategoryFormErrors,
  createEmptyTranslations,
  getAllLanguageCodes,
  LANGUAGE_NAMES,
  LANGUAGE_ENGLISH_NAMES
} from '@/lib/types/category';
import { CategorySelector } from './CategorySelector';

interface CategoryFormProps {
  category?: Category | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function CategoryForm({ category, onSubmit, onCancel }: CategoryFormProps) {
  const [formData, setFormData] = useState<CategoryFormData>({
    parentId: undefined,
    translationsJson: '',
    type: CategoryType.PRODUCT,
    level: 1
  });
  
  const [errors, setErrors] = useState<CategoryFormErrors>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (category) {
      // Editing existing category - normalize translation keys to uppercase
      const normalizedTranslations: { [key: string]: string } = {};
      Object.entries(category.translations).forEach(([key, value]) => {
        normalizedTranslations[key.toUpperCase()] = value;
      });

      setFormData({
        parentId: category.parentId,
        translationsJson: JSON.stringify(normalizedTranslations, null, 2),
        type: category.type,
        level: category.level
      });
    } else {
      // Creating new category - use all language codes from backend
      setFormData({
        parentId: undefined,
        translationsJson: JSON.stringify(createEmptyTranslations(), null, 2),
        type: CategoryType.PRODUCT,
        level: 1
      });

    }

  }, [category]);







  const validateForm = (): boolean => {
    const newErrors: CategoryFormErrors = {};

    // Validate translations JSON
    try {
      const translations = categoryService.parseTranslationsJson(formData.translationsJson);
      const translationErrors = categoryService.validateTranslations(translations);
      if (translationErrors.length > 0) {
        newErrors.translationsJson = translationErrors.join(', ');
      }
    } catch (error) {
      newErrors.translationsJson = error instanceof Error ? error.message : 'Invalid JSON format';
    }

    // Validate type
    if (!formData.type) {
      newErrors.type = 'Category type is required';
    }

    // Validate level
    if (formData.level < 1) {
      newErrors.level = 'Level must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateLevel = async (parentId?: number): Promise<number> => {
    if (!parentId) return 1;
    return await categoryService.calculateLevel(parentId);
  };

  const handleParentChange = async (parentId: number | null) => {
    const newLevel = await calculateLevel(parentId || undefined);

    setFormData(prev => ({
      ...prev,
      parentId: parentId || undefined,
      level: newLevel
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const translations = categoryService.parseTranslationsJson(formData.translationsJson);
      
      if (category) {
        // Update existing category
        const updateRequest: UpdateCategoryRequest = {
          parentId: formData.parentId,
          translations,
          type: formData.type,
          level: formData.level
        };
        await categoryService.updateCategory(category.id, updateRequest);
      } else {
        // Create new category
        const createRequest: CreateCategoryRequest = {
          parentId: formData.parentId,
          translations,
          type: formData.type,
          level: formData.level
        };
        await categoryService.createCategory(createRequest);
      }
      
      onSubmit();
    } catch (error) {
      // Handle save error
      setErrors({ 
        translationsJson: error instanceof Error ? error.message : 'Failed to save category' 
      });
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              {category ? 'Edit Category' : 'Create New Category'}
            </CardTitle>
            {category && (
              <div className="mt-1">
                {category.translations.EN && (
                  <p className="text-sm font-medium text-gray-800">
                    {category.translations.EN}
                  </p>
                )}
                {category.translations.TR && (
                  <p className="text-sm text-gray-600">
                    {category.translations.TR}
                  </p>
                )}
              </div>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Category Type */}
            <div className="space-y-2">
              <Label htmlFor="type">Category Type *</Label>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={formData.type === CategoryType.PRODUCT ? 'default' : 'outline'}
                  onClick={() => setFormData(prev => ({ ...prev, type: CategoryType.PRODUCT }))}
                >
                  Product
                </Button>
                <Button
                  type="button"
                  variant={formData.type === CategoryType.SERVICE ? 'default' : 'outline'}
                  onClick={() => setFormData(prev => ({ ...prev, type: CategoryType.SERVICE }))}
                >
                  Service
                </Button>
              </div>
              {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
            </div>

            {/* Parent Category */}
            <div className="space-y-2">
              <Label htmlFor="parentId">Parent Category</Label>
              <CategorySelector
                value={formData.parentId || null}
                onChange={handleParentChange}
                placeholder="Search for parent category or leave empty for root category"
                searchPlaceholder="Search parent categories..."
                className="w-full"
                includeAllOption={true}
                allOptionLabel="No Parent (Root Category)"
              />
              {errors.parentId && <p className="text-sm text-red-600">{errors.parentId}</p>}
            </div>

            {/* Level Display */}
            <div className="space-y-2">
              <Label>Category Level</Label>
              <Badge variant="outline">Level {formData.level}</Badge>
            </div>

            {/* Translations JSON */}
            <div className="space-y-2">
              <Label htmlFor="translations">Translations (JSON) *</Label>
              <textarea
                id="translations"
                value={formData.translationsJson}
                onChange={(e) => setFormData(prev => ({ ...prev, translationsJson: e.target.value }))}
                className="flex min-h-[256px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"
                placeholder={`Enter translations as JSON for all supported languages:\n${JSON.stringify(createEmptyTranslations(), null, 2)}`}
              />
              {errors.translationsJson && (
                <p className="text-sm text-red-600">{errors.translationsJson}</p>
              )}
              <div className="text-sm text-gray-500">
                <p className="mb-2">Enter translations as JSON. English (EN) is required. Supported languages:</p>
                <div className="grid grid-cols-3 gap-2 text-xs">
                  {getAllLanguageCodes().map(code => (
                    <span key={code} className="flex items-center space-x-1">
                      <code className="bg-gray-100 px-1 rounded">{code}</code>
                      <span>{LANGUAGE_ENGLISH_NAMES[code]}</span>
                    </span>
                  ))}
                </div>
                <p className="mt-2">
                  Example: <code className="bg-gray-100 px-1 rounded text-xs">
                    {`{"EN": "Computer", "TR": "Bilgisayar", "DE": "Computer"}`}
                  </code>
                </p>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : (category ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
