'use client';

import { useEffect, useState } from 'react';
import type { DropResult } from '@hello-pangea/dnd';

interface DragDropWrapperProps {
  children: React.ReactNode;
  onDragEnd: (result: DropResult) => void;
  droppableId: string;
}

export function DragDropWrapper({ children, onDragEnd, droppableId }: DragDropWrapperProps) {
  const [isClient, setIsClient] = useState(false);
  const [DragDropContext, setDragDropContext] = useState<any>(null);
  const [Droppable, setDroppable] = useState<any>(null);

  useEffect(() => {
    const loadDragDrop = async () => {
      try {
        const dnd = await import('@hello-pangea/dnd');
        setDragDropContext(() => dnd.DragDropContext);
        setDroppable(() => dnd.Droppable);
        setIsClient(true);
      } catch (error) {
        console.error('Failed to load drag and drop:', error);
        setIsClient(true); // Still set to true to show fallback
      }
    };

    loadDragDrop();
  }, []);

  if (!isClient || !DragDropContext || !Droppable) {
    return <div className="space-y-2 min-h-[200px]">{children}</div>;
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId={droppableId}>
        {(provided: any) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="space-y-2 min-h-[200px]"
          >
            {children}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}

interface DraggableItemProps {
  children: React.ReactNode;
  draggableId: string;
  index: number;
}

export function DraggableItem({ children, draggableId, index }: DraggableItemProps) {
  const [isClient, setIsClient] = useState(false);
  const [Draggable, setDraggable] = useState<any>(null);

  useEffect(() => {
    const loadDraggable = async () => {
      try {
        const dnd = await import('@hello-pangea/dnd');
        setDraggable(() => dnd.Draggable);
        setIsClient(true);
      } catch (error) {
        console.error('Failed to load draggable:', error);
        setIsClient(true);
      }
    };

    loadDraggable();
  }, []);

  if (!isClient || !Draggable) {
    return <div>{children}</div>;
  }

  return (
    <Draggable draggableId={draggableId} index={index}>
      {(provided: any) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
        >
          {children}
        </div>
      )}
    </Draggable>
  );
}
