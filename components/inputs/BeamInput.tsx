import {
  animate,
  useMotionTemplate,
  useMotionValue,
  motion,
} from "framer-motion";
import React, { useEffect, useRef } from "react";
import { FiArrowRight } from "react-icons/fi";

interface BeamInputProps {
  placeholder?: string;
  buttonText?: string;
  onSubmit?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  variant?: "purple" | "blue" | "green" | "red";
  type?: "email" | "text" | "search";
}

const BeamInput = ({
  placeholder = "Enter your email",
  buttonText = "Join Waitlist",
  onSubmit,
  className = "",
  disabled = false,
  variant = "purple",
  type = "email"
}: BeamInputProps) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const turn = useMotionValue(0);

  useEffect(() => {
    if (!disabled) {
      animate(turn, 1, {
        ease: "linear",
        duration: 5,
        repeat: Infinity,
      });
    }
  }, [disabled, turn]);

  const getVariantColor = () => {
    switch (variant) {
      case "blue":
        return "#60a5fa";
      case "green":
        return "#34d399";
      case "red":
        return "#f87171";
      default:
        return "#a78bfa";
    }
  };

  const color = getVariantColor();
  const backgroundImage = useMotionTemplate`conic-gradient(from ${turn}turn, ${color}00 75%, ${color} 100%)`;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (disabled || !inputRef.current) return;
    
    const value = inputRef.current.value;
    if (onSubmit && value.trim()) {
      onSubmit(value);
      inputRef.current.value = "";
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      onClick={() => {
        if (!disabled) {
          inputRef.current?.focus();
        }
      }}
      className={`relative flex w-full max-w-md items-center gap-2 rounded-full border border-white/20 bg-gradient-to-br from-white/20 to-white/5 py-1.5 pl-6 pr-1.5 ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
    >
      <input
        ref={inputRef}
        type={type}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full bg-transparent text-sm text-white placeholder-white/80 focus:outline-0 disabled:cursor-not-allowed"
      />

      <button
        onClick={(e) => e.stopPropagation()}
        type="submit"
        disabled={disabled}
        className="group flex shrink-0 items-center gap-1.5 rounded-full bg-gradient-to-br from-gray-50 to-gray-400 px-4 py-3 text-sm font-medium text-gray-900 transition-transform active:scale-[0.985] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span>{buttonText}</span>
        <FiArrowRight className="-mr-4 opacity-0 transition-all group-hover:-mr-0 group-hover:opacity-100 group-active:-rotate-45" />
      </button>

      {!disabled && (
        <div className="pointer-events-none absolute inset-0 z-10 rounded-full">
          <motion.div
            style={{
              backgroundImage,
            }}
            className="mask-with-browser-support absolute -inset-[1px] rounded-full border border-transparent bg-origin-border"
          />
        </div>
      )}
    </form>
  );
};

export default BeamInput;
