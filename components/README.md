# Components Library

Bu klasör, admin-panel projesindeki some-components klasöründen webapp projesine uyarlanmış UI componentlerini içerir.

## 📁 Klasör Yapısı

```
components/
├── buttons/           # İnteraktif button componentleri
├── forms/            # Form componentleri
├── inputs/           # Input componentleri
├── loaders/          # Loading state componentleri
├── notifications/    # Bildirim componentleri
├── tabs/             # Tab componentleri
├── toggles/          # Toggle switch componentleri
├── layout/           # Layout componentleri
├── ui/              # Temel UI componentleri
├── users/           # Kullanıcı yönetimi componentleri
└── index.ts         # Tüm componentlerin export'u
```

## 🎨 Mevcut Componentler

### Buttons
- **EncryptButton**: Hover'da text scrambling efekti olan button
- **LoadAndErrorButton**: Loading, success, error state'leri olan button
- **ShinyButton**: Hover'da shine efekti olan button
- **SpotlightButton**: Mouse takibi ile spotlight efekti olan button
- **DrawOutlineButton**: Hover'da outline çizimi olan button
- **MarqueeButton**: Marquee text animasyonu olan button
- **WetPaintButton**: Wet paint drip efekti olan button

### Forms
- **ShiftingContactForm**: Animasyonlu contact form

### Loaders
- **SpinLoader**: Dönen loading indicator
- **PulseLoader**: Pulse efekti olan loading dots

### Notifications
- **Toast**: Animasyonlu toast notification
- **ToastContainer**: Toast'ları yöneten container

### Tabs
- **ChipTabs**: Chip style tab navigation
- **SlideTabs**: Sliding cursor tab navigation

### Toggles
- **DarkModeToggle**: Animasyonlu dark/light mode toggle
- **SliderToggle**: Slider style toggle switch

### Inputs
- **BeamInput**: Rotating beam border input field

## 🚀 Kullanım

### Tek tek import:
```tsx
import EncryptButton from '@/components/buttons/EncryptButton';
import { Toast, ToastContainer } from '@/components/notifications/Toast';
```

### Index'ten import:
```tsx
import { EncryptButton, Toast, ToastContainer } from '@/components';
```

## 📦 Gerekli Dependencies

```json
{
  "framer-motion": "^11.0.0",
  "react-icons": "^5.0.0",
  "tailwindcss": "^4"
}
```

## 🎯 Özellikler

- ✅ TypeScript desteği
- ✅ Framer Motion animasyonları
- ✅ Tailwind CSS styling
- ✅ Responsive design
- ✅ Accessibility desteği
- ✅ Customizable props

## 📝 Örnek Kullanım

```tsx
// Button örneği
<EncryptButton 
  text="Secure Login"
  onClick={() => console.log('Clicked!')}
  className="my-custom-class"
/>

// Toast örneği
const [toasts, setToasts] = useState([]);

const addToast = (type, title, message) => {
  const newToast = {
    id: Date.now().toString(),
    type,
    title,
    message
  };
  setToasts(prev => [...prev, newToast]);
};

<ToastContainer 
  toasts={toasts}
  onClose={(id) => setToasts(prev => prev.filter(t => t.id !== id))}
  position="top-right"
/>
```

## 🔧 Geliştirme

Yeni component eklemek için:
1. İlgili klasörde component'i oluştur
2. `components/index.ts`'e export ekle
3. README'yi güncelle

## 📄 Lisans

Bu componentler LookForX projesi kapsamında geliştirilmiştir.
