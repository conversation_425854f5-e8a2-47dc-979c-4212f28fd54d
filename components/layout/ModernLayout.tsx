'use client';

import React, { useState, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SimpleProfileImage from '@/components/ui/SimpleProfileImage';
import BellNotification from '@/components/notifications/BellNotification';
import Link from 'next/link';
import {
  User,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  BarChart3,
  Bell,
  Search,
  ChevronDown,
  Shield,
  FolderTree,
  Database,
  AlertTriangle,
  FileText,
  Activity,
  HelpCircle,
} from 'lucide-react';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

// Utility function to convert ObjectId string to Long (matching backend logic)
function objectIdToLong(objectId: string): string {
  let hash = 0;
  for (let i = 0; i < objectId.length; i++) {
    const char = objectId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  // Return the hash as-is (can be negative, matching Java's hashCode())
  return hash.toString();
}

interface ModernLayoutProps {
  children: React.ReactNode;
}

const ModernLayout = ({ children }: ModernLayoutProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout, loading } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const navigation = useMemo(() => [
    { name: 'Dashboard', href: '/dashboard', icon: Home, current: pathname === '/dashboard' },
    { name: 'Users', href: '/users', icon: Users, current: pathname.startsWith('/users') },
    { name: 'Categories', href: '/categories', icon: FolderTree, current: pathname.startsWith('/categories') },
    { name: 'Forms', href: '/forms', icon: FileText, current: pathname.startsWith('/forms') && !pathname.startsWith('/forms/submissions') },
    { name: 'Form Submissions', href: '/forms/submissions', icon: FileText, current: pathname.startsWith('/forms/submissions') },
    { name: 'Events', href: '/events', icon: Activity, current: pathname.startsWith('/events') },
    { name: 'Notifications', href: '/notifications', icon: Bell, current: pathname.startsWith('/notifications') && !pathname.startsWith('/bulk-notifications') },
    { name: 'Bulk Notifications', href: '/bulk-notifications', icon: Bell, current: pathname.startsWith('/bulk-notifications') },
    { name: 'Reference Data', href: '/reference-data', icon: Database, current: pathname.startsWith('/reference-data') },
    { name: 'Exceptions', href: '/exceptions', icon: AlertTriangle, current: pathname.startsWith('/exceptions') },
    { name: 'Analytics', href: '/analytics', icon: BarChart3, current: pathname === '/analytics' },
    { name: 'Help', href: '/help', icon: HelpCircle, current: pathname.startsWith('/help') },
    { name: 'Settings', href: '/settings', icon: Settings, current: pathname === '/settings' },
  ], [pathname]);

  if (loading) {
    return <LoadingSpinner />;
  }

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-background/80" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-background shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b border-border">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600">
                <span className="text-sm font-bold text-white">L</span>
              </div>
              <span className="ml-2 text-lg font-semibold text-foreground">LookForX</span>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-1">
            {navigation.map((item) => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  prefetch
                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    item.current
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-r-2 border-blue-700 dark:border-blue-400'
                      : 'text-foreground hover:bg-muted hover:text-foreground'
                  }`}
                >
                  <IconComponent className={`mr-3 h-5 w-5 ${item.current ? 'text-blue-700 dark:text-blue-400' : 'text-muted-foreground group-hover:text-foreground'}`} />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-background border-r border-border shadow-sm">
          <div className="flex h-16 items-center px-4 border-b border-border">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600">
                <span className="text-sm font-bold text-white">L</span>
              </div>
              <span className="ml-2 text-lg font-semibold text-foreground">LookForX</span>
            </div>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-1">
            {navigation.map((item) => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  prefetch
                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    item.current
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-r-2 border-blue-700 dark:border-blue-400'
                      : 'text-foreground hover:bg-muted hover:text-foreground'
                  }`}
                >
                  <IconComponent className={`mr-3 h-5 w-5 ${item.current ? 'text-blue-700 dark:text-blue-400' : 'text-muted-foreground group-hover:text-foreground'}`} />
                  {item.name}
                </Link>
              );
            })}
          </nav>
          
          {/* User info at bottom */}
          <div className="flex-shrink-0 border-t border-border p-4">
            <div className="flex items-center">
              <SimpleProfileImage
                src={user?.imageUrl}
                alt={user?.name || 'User'}
                size="sm"
              />
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">{user?.name || 'Admin'}</p>
                <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top navigation */}
        <div className="sticky top-0 z-40 bg-background border-b border-border shadow-sm">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>
              {/* Search bar */}
              <div className="hidden sm:block ml-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-10 pr-4 py-2 border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64 bg-background text-foreground placeholder:text-muted-foreground"
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <BellNotification
                userId={user?.id ? objectIdToLong(user.id) : "1"}
                languageCode="EN"
              />
              {/* User menu */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2"
                >
                  <SimpleProfileImage
                    src={user?.imageUrl}
                    alt={user?.name || 'User'}
                    size="sm"
                  />
                  <span className="hidden sm:block text-sm font-medium text-foreground">{user?.name || 'Admin'}</span>
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                </Button>
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-background rounded-lg shadow-lg border border-border py-1 z-50">
                    <div className="px-4 py-2 border-b border-border">
                      <p className="text-sm font-medium text-foreground">{user?.name || 'Admin'}</p>
                      <p className="text-xs text-muted-foreground">{user?.email}</p>
                      <div className="flex items-center mt-1">
                        <Badge variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          {user?.roles?.join(', ') || 'Admin'}
                        </Badge>
                      </div>
                    </div>
                    <button
                      onClick={() => router.push(`/users/${user?.id}`)}
                      className="block w-full text-left px-4 py-2 text-sm text-foreground hover:bg-muted"
                    >
                      <User className="inline h-4 w-4 mr-2" />
                      Profile
                    </button>
                    <a href="/settings" className="block px-4 py-2 text-sm text-foreground hover:bg-muted">
                      <Settings className="inline h-4 w-4 mr-2" />
                      Settings
                    </a>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 dark:hover:bg-red-900/30"
                    >
                      <LogOut className="inline h-4 w-4 mr-2" />
                      Sign out
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ModernLayout;
