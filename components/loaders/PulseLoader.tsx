import { motion } from "framer-motion";

interface PulseLoaderProps {
  size?: "sm" | "md" | "lg";
  color?: string;
  className?: string;
}

const PulseLoader = ({ 
  size = "md", 
  color = "bg-indigo-600",
  className = ""
}: PulseLoaderProps) => {
  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3", 
    lg: "w-4 h-4"
  };

  const dots = [0, 1, 2];

  return (
    <div className={`flex items-center justify-center space-x-1 ${className}`}>
      {dots.map((dot) => (
        <motion.div
          key={dot}
          className={`${sizeClasses[size]} ${color} rounded-full`}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: dot * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

export default PulseLoader;
