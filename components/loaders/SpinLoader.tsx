import { motion } from "framer-motion";

interface SpinLoaderProps {
  size?: "sm" | "md" | "lg";
  color?: string;
  className?: string;
}

const SpinLoader = ({ 
  size = "md", 
  color = "text-indigo-600",
  className = ""
}: SpinLoaderProps) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        className={`${sizeClasses[size]} border-2 border-gray-200 rounded-full ${color}`}
        style={{
          borderTopColor: "currentColor"
        }}
        animate={{
          rotate: 360
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};

export default SpinLoader;
