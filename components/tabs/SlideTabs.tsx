import React, { Di<PERSON><PERSON>, SetStateAction, useRef, useState } from "react";
import { motion } from "framer-motion";

interface SlideTabsProps {
  tabs: string[];
  onTabChange?: (tab: string) => void;
  className?: string;
  variant?: "black" | "blue" | "green" | "red";
}

interface TabProps {
  children: string;
  setPosition: Dispatch<SetStateAction<Position>>;
  onClick?: () => void;
}

interface CursorProps {
  position: Position;
  variant: "black" | "blue" | "green" | "red";
}

type Position = {
  left: number;
  width: number;
  opacity: number;
};

const SlideTabs = ({ 
  tabs, 
  onTabChange,
  className = "",
  variant = "black"
}: SlideTabsProps) => {
  const [position, setPosition] = useState<Position>({
    left: 0,
    width: 0,
    opacity: 0,
  });

  const handleTabClick = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  const getBorderColor = () => {
    switch (variant) {
      case "blue":
        return "border-blue-500";
      case "green":
        return "border-green-500";
      case "red":
        return "border-red-500";
      default:
        return "border-black";
    }
  };

  return (
    <ul
      onMouseLeave={() => {
        setPosition((pv) => ({
          ...pv,
          opacity: 0,
        }));
      }}
      className={`relative mx-auto flex w-fit rounded-full border-2 ${getBorderColor()} bg-white p-1 ${className}`}
    >
      {tabs.map((tab) => (
        <Tab 
          key={tab}
          setPosition={setPosition}
          onClick={() => handleTabClick(tab)}
        >
          {tab}
        </Tab>
      ))}

      <Cursor position={position} variant={variant} />
    </ul>
  );
};

const Tab = ({
  children,
  setPosition,
  onClick,
}: TabProps) => {
  const ref = useRef<null | HTMLLIElement>(null);

  return (
    <li
      ref={ref}
      onMouseEnter={() => {
        if (!ref?.current) return;

        const { width } = ref.current.getBoundingClientRect();

        setPosition({
          left: ref.current.offsetLeft,
          width,
          opacity: 1,
        });
      }}
      onClick={onClick}
      className="relative z-10 block cursor-pointer px-3 py-1.5 text-xs uppercase text-white mix-blend-difference md:px-5 md:py-3 md:text-base"
    >
      {children}
    </li>
  );
};

const Cursor = ({ position, variant }: CursorProps) => {
  const getBgColor = () => {
    switch (variant) {
      case "blue":
        return "bg-blue-500";
      case "green":
        return "bg-green-500";
      case "red":
        return "bg-red-500";
      default:
        return "bg-black";
    }
  };

  return (
    <motion.li
      animate={{
        ...position,
      }}
      className={`absolute z-0 h-7 rounded-full ${getBgColor()} md:h-12`}
    />
  );
};

export default SlideTabs;
