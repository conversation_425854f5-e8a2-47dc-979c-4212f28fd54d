import { motion } from "framer-motion";
import { Dispatch, SetStateAction, useState } from "react";

interface ChipTabsProps {
  tabs: string[];
  defaultSelected?: string;
  onTabChange?: (tab: string) => void;
  className?: string;
  variant?: "violet" | "blue" | "green" | "red";
}

interface ChipProps {
  text: string;
  selected: boolean;
  setSelected: Dispatch<SetStateAction<string>>;
  variant: "violet" | "blue" | "green" | "red";
}

const ChipTabs = ({ 
  tabs, 
  defaultSelected,
  onTabChange,
  className = "",
  variant = "violet"
}: ChipTabsProps) => {
  const [selected, setSelected] = useState(defaultSelected || tabs[0]);

  const handleTabChange = (tab: string) => {
    setSelected(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  const getGradientColors = () => {
    switch (variant) {
      case "blue":
        return "from-blue-600 to-cyan-600";
      case "green":
        return "from-green-600 to-emerald-600";
      case "red":
        return "from-red-600 to-pink-600";
      default:
        return "from-violet-600 to-indigo-600";
    }
  };

  return (
    <div className={`px-4 py-14 bg-slate-900 flex items-center flex-wrap gap-2 ${className}`}>
      {tabs.map((tab) => (
        <Chip
          text={tab}
          selected={selected === tab}
          setSelected={handleTabChange}
          variant={variant}
          key={tab}
        />
      ))}
    </div>
  );
};

const Chip = ({
  text,
  selected,
  setSelected,
  variant,
}: ChipProps) => {
  const getGradientColors = () => {
    switch (variant) {
      case "blue":
        return "from-blue-600 to-cyan-600";
      case "green":
        return "from-green-600 to-emerald-600";
      case "red":
        return "from-red-600 to-pink-600";
      default:
        return "from-violet-600 to-indigo-600";
    }
  };

  return (
    <button
      onClick={() => setSelected(text)}
      className={`${
        selected
          ? "text-white"
          : "text-slate-300 hover:text-slate-200 hover:bg-slate-700"
      } text-sm transition-colors px-2.5 py-0.5 rounded-md relative`}
    >
      <span className="relative z-10">{text}</span>
      {selected && (
        <motion.span
          layoutId="pill-tab"
          transition={{ type: "spring", duration: 0.5 }}
          className={`absolute inset-0 z-0 bg-gradient-to-r ${getGradientColors()} rounded-md`}
        ></motion.span>
      )}
    </button>
  );
};

export default ChipTabs;
