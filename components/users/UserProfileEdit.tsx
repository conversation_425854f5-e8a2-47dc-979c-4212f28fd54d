'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  User, 
  Building2, 
  Phone, 
  MapPin, 
  Save
} from 'lucide-react';
import { UserProfile, userService } from '@/lib/user-service';
import SimpleProfileImage from '@/components/ui/SimpleProfileImage';
import { useToast } from '@/contexts/ToastContext';

interface UserProfileEditProps {
  userId: string;
  onSave?: () => void;
}

export function UserProfileEdit({ userId, onSave }: UserProfileEditProps) {
  console.log('=== UserProfileEdit RENDERED ===', { userId });

  const toast = useToast();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    userType: 'REGULAR' as 'REGULAR' | 'COMPANY',
    birthDate: '',
    nationality: '',
    preferredLanguage: 'tr',
    mobilePhone: '',
    landlinePhone: '',
    country: '',
    city: '',
    district: '',
    neighborhood: '',
    street: '',
    doorNumber: '',
    postalCode: '',
    fullAddress: '',
    companyName: '',
    taxNumber: '',
    companyType: '',
    website: '',
    companyAddress: '',
    profileVisibility: 'PUBLIC' as 'PUBLIC' | 'CONNECTIONS_ONLY' | 'PRIVATE'
  });

  useEffect(() => {
    fetchUserProfile();
  }, [userId]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profileData = await userService.getUserProfile(userId);
      setProfile(profileData);
      setFormData({
        username: profileData.username || '',
        firstName: profileData.firstName || '',
        lastName: profileData.lastName || '',
        userType: profileData.userType || 'REGULAR',
        birthDate: profileData.birthDate || '',
        nationality: profileData.nationality || '',
        preferredLanguage: profileData.preferredLanguage || 'tr',
        mobilePhone: profileData.mobilePhone || '',
        landlinePhone: profileData.landlinePhone || '',
        country: profileData.country || '',
        city: profileData.city || '',
        district: profileData.district || '',
        neighborhood: profileData.neighborhood || '',
        street: profileData.street || '',
        doorNumber: profileData.doorNumber || '',
        postalCode: profileData.postalCode || '',
        fullAddress: profileData.fullAddress || '',
        companyName: profileData.companyName || '',
        taxNumber: profileData.taxNumber || '',
        companyType: profileData.companyType || '',
        website: profileData.website || '',
        companyAddress: profileData.companyAddress || '',
        profileVisibility: profileData.profileVisibility || 'PUBLIC'
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
      toast.error('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validation functions
  const validateField = (field: string, value: string): string | null => {
    if (!value || value.trim() === '') return null; // Empty is valid

    switch (field) {
      case 'username':
        if (!/^[a-zA-Z0-9_]{3,30}$/.test(value)) {
          return 'Username must be 3-30 characters with letters, numbers, and underscores only';
        }
        break;
      case 'mobilePhone':
      case 'landlinePhone':
        if (!/^\+?[1-9]\d{1,14}$/.test(value)) {
          return 'Invalid phone format (e.g., +905551234567)';
        }
        break;
      case 'postalCode':
        if (!/^\d{5}$/.test(value)) {
          return 'Postal code must be 5 digits';
        }
        break;
      case 'website':
        if (!/^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(value)) {
          return 'Invalid website URL format';
        }
        break;
      case 'preferredLanguage':
        if (!['tr', 'en', 'de', 'fr', 'es'].includes(value as string)) {
          return 'Language must be one of: tr, en, de, fr, es';
        }
        break;
      case 'fullAddress':
      case 'companyAddress':
        if ((value as string).length > 500) {
          return 'Address cannot exceed 500 characters';
        }
        break;
    }
    return null;
  };

  const getFieldError = (field: string): string | null => {
    return validateField(field, formData[field as keyof typeof formData] as string);
  };

  const handleSave = async () => {
    console.log('=== HANDLE SAVE CALLED ===');
    console.log('Current formData:', formData);

    // Validate all fields before saving
    const validationErrors: string[] = [];
    Object.keys(formData).forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData] as string);
      if (error) {
        validationErrors.push(`${field}: ${error}`);
      }
    });

    if (validationErrors.length > 0) {
      toast.error('Please fix validation errors:\n' + validationErrors.join('\n'));
      return;
    }

    try {
      setSaving(true);

      // Prepare update data
      const updateData = {
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName,
        userType: formData.userType,
        birthDate: formData.birthDate || undefined,
        nationality: formData.nationality,
        preferredLanguage: formData.preferredLanguage,
        mobilePhone: formData.mobilePhone,
        landlinePhone: formData.landlinePhone,
        country: formData.country,
        city: formData.city,
        district: formData.district,
        neighborhood: formData.neighborhood,
        street: formData.street,
        doorNumber: formData.doorNumber,
        postalCode: formData.postalCode,
        fullAddress: formData.fullAddress,
        companyName: formData.companyName,
        taxNumber: formData.taxNumber,
        companyType: formData.companyType,
        website: formData.website,
        companyAddress: formData.companyAddress,
        profileVisibility: formData.profileVisibility
      };

      console.log('Updating profile for userId:', userId);
      console.log('Update data:', updateData);

      // Test with simple endpoint first
      try {
        const testUrl = `http://localhost:8081/api/v1/test/update-profile-simple?userId=${userId}&firstName=${encodeURIComponent(updateData.firstName || '')}&lastName=${encodeURIComponent(updateData.lastName || '')}&mobilePhone=${encodeURIComponent(updateData.mobilePhone || '')}`;
        console.log('Testing with simple endpoint:', testUrl);

        const testResponse = await fetch(testUrl, { method: 'POST' });
        const testResult = await testResponse.text();
        console.log('Simple test result:', testResult);
      } catch (testError) {
        console.error('Simple test failed:', testError);
      }

      try {
        await userService.updateUserProfile(userId, updateData);
        // Profile updated successfully - don't show toast here, let parent handle it

        if (onSave) {
          onSave();
        }
      } catch (apiError: unknown) {
        // Handle API error

        if ((apiError as unknown as { response?: { status?: number } }).response?.status === 403) {
          toast.error('Access denied. Admin privileges required.');
        } else if ((apiError as unknown as { response?: { status?: number } }).response?.status === 401) {
          toast.error('Authentication failed. Please login again.');
        } else {
          toast.error('Failed to update profile: ' + ((apiError as unknown as { response?: { data?: { message?: string } }, message?: string })?.response?.data?.message || (apiError as unknown as { message?: string })?.message));
        }
        return; // Don't call onSave if API failed
      }
    } catch (error: unknown) {
      console.error('Error updating profile:', error);
      console.error('Error details:', error);
      toast.error('Failed to update profile: ' + ((error as unknown as { message?: string })?.message || 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <User className="h-12 w-12 mx-auto mb-2" />
            <p>No profile found for this user</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Row: Basic Information & Address Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
            <CardDescription>
              Personal information and account details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Profile Photo */}
            <div className="flex justify-center mb-6">
              <div className="text-center">
                <SimpleProfileImage
                  src={profile.profilePhotoUrl || profile.profileImageUrl}
                  alt={profile.firstName + ' ' + profile.lastName}
                  size="xl"
                />
                <p className="text-sm text-gray-500 mt-2">Profile Photo</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  placeholder="Enter username (optional)"
                  className={getFieldError('username') ? 'border-red-500' : ''}
                />
                {getFieldError('username') && (
                  <p className="text-sm text-red-600">{getFieldError('username')}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="userType">User Type</Label>
                <select
                  id="userType"
                  value={formData.userType}
                  onChange={(e) => handleInputChange('userType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="REGULAR">Individual</option>
                  <option value="COMPANY">Company</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Enter first name (optional)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Enter last name (optional)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="birthDate">Birth Date</Label>
                <Input
                  id="birthDate"
                  type="date"
                  value={formData.birthDate}
                  onChange={(e) => handleInputChange('birthDate', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nationality">Nationality</Label>
                <Input
                  id="nationality"
                  value={formData.nationality}
                  onChange={(e) => handleInputChange('nationality', e.target.value)}
                  placeholder="Enter nationality"
                />
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Address Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  placeholder="Turkey"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Istanbul"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="district">District</Label>
                <Input
                  id="district"
                  value={formData.district}
                  onChange={(e) => handleInputChange('district', e.target.value)}
                  placeholder="Kadıköy"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="neighborhood">Neighborhood</Label>
                <Input
                  id="neighborhood"
                  value={formData.neighborhood}
                  onChange={(e) => handleInputChange('neighborhood', e.target.value)}
                  placeholder="Moda"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="street">Street</Label>
                <Input
                  id="street"
                  value={formData.street}
                  onChange={(e) => handleInputChange('street', e.target.value)}
                  placeholder="Bahariye Caddesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="doorNumber">Door Number</Label>
                <Input
                  id="doorNumber"
                  value={formData.doorNumber}
                  onChange={(e) => handleInputChange('doorNumber', e.target.value)}
                  placeholder="123/4"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  placeholder="34710"
                  className={getFieldError('postalCode') ? 'border-red-500' : ''}
                />
                {getFieldError('postalCode') && (
                  <p className="text-sm text-red-600">{getFieldError('postalCode')}</p>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="fullAddress">Full Address</Label>
              <textarea
                id="fullAddress"
                value={formData.fullAddress}
                onChange={(e) => handleInputChange('fullAddress', e.target.value)}
                placeholder="Complete address..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Row: Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Phone className="h-5 w-5" />
            <span>Contact Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="mobilePhone">Mobile Phone</Label>
              <Input
                id="mobilePhone"
                value={formData.mobilePhone}
                onChange={(e) => handleInputChange('mobilePhone', e.target.value)}
                placeholder="+90 ************"
                className={getFieldError('mobilePhone') ? 'border-red-500' : ''}
              />
              {getFieldError('mobilePhone') && (
                <p className="text-sm text-red-600">{getFieldError('mobilePhone')}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="landlinePhone">Landline Phone</Label>
              <Input
                id="landlinePhone"
                value={formData.landlinePhone}
                onChange={(e) => handleInputChange('landlinePhone', e.target.value)}
                placeholder="+90 ************"
                className={getFieldError('landlinePhone') ? 'border-red-500' : ''}
              />
              {getFieldError('landlinePhone') && (
                <p className="text-sm text-red-600">{getFieldError('landlinePhone')}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Company Information (if user type is COMPANY) */}
      {formData.userType === 'COMPANY' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="h-5 w-5" />
              <span>Company Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companyName">Company Name</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  placeholder="Company Ltd."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="taxNumber">Tax Number</Label>
                <Input
                  id="taxNumber"
                  value={formData.taxNumber}
                  onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                  placeholder="1234567890"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="companyType">Company Type</Label>
                <Input
                  id="companyType"
                  value={formData.companyType}
                  onChange={(e) => handleInputChange('companyType', e.target.value)}
                  placeholder="Limited Company"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://company.com"
                  className={getFieldError('website') ? 'border-red-500' : ''}
                />
                {getFieldError('website') && (
                  <p className="text-sm text-red-600">{getFieldError('website')}</p>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="companyAddress">Company Address</Label>
              <textarea
                id="companyAddress"
                value={formData.companyAddress}
                onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                placeholder="Company address..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Button */}
      {/* Hidden Save Profile Button - triggered by top button */}
      <div className="hidden">
        <Button
          data-save-profile
          onClick={() => {
            console.log('=== SAVE BUTTON CLICKED ===');
            handleSave();
          }}
          disabled={saving}
        >
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>
    </div>
  );
}
