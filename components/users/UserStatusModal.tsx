'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { X, UserX, UserCheck, AlertTriangle } from 'lucide-react';
import { User } from '@/lib/user-service';

interface UserStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  onUpdate: (userId: string, active: boolean) => Promise<void>;
}

export default function UserStatusModal({ 
  isOpen, 
  onClose, 
  user, 
  onUpdate 
}: UserStatusModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Handle status update
  const handleStatusUpdate = async (active: boolean) => {
    try {
      setLoading(true);
      setError('');
      await onUpdate(user.id, active);
      onClose();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to update user status');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const isActivating = !user.active;
  const actionText = isActivating ? 'Activate' : 'Deactivate';
  const actionColor = isActivating ? 'green' : 'red';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
              <CardTitle className="text-xl">{actionText} User</CardTitle>
              <CardDescription>
                {isActivating 
                  ? 'Restore user access to the system'
                  : 'Remove user access to the system'
                }
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* User Info */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex h-10 w-10 shrink-0 overflow-hidden rounded-full">
                {user.imageUrl ? (
                  <img 
                    src={user.imageUrl} 
                    alt={user.name} 
                    className="aspect-square h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center rounded-full bg-blue-100 text-blue-600 text-sm font-semibold">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium text-gray-900">{user.name}</div>
                <div className="text-sm text-gray-500">{user.email}</div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge 
                    variant={user.active ? "default" : "secondary"}
                    className={user.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                  >
                    {user.active ? "Active" : "Inactive"}
                  </Badge>
                  <div className="flex flex-wrap gap-1">
                    {user.roles.map((role) => (
                      <Badge key={role} variant="outline" className="text-xs">
                        {role}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Warning/Info Message */}
            <Alert className={isActivating ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              <div className="flex items-center gap-2">
                {isActivating ? (
                  <UserCheck className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={isActivating ? "text-green-800" : "text-red-800"}>
                  {isActivating ? (
                    <>
                      <strong>Activating this user will:</strong>
                      <ul className="mt-2 ml-4 list-disc space-y-1">
                        <li>Restore their access to the system</li>
                        <li>Allow them to log in again</li>
                        <li>Re-enable all their permissions</li>
                      </ul>
                    </>
                  ) : (
                    <>
                      <strong>Deactivating this user will:</strong>
                      <ul className="mt-2 ml-4 list-disc space-y-1">
                        <li>Immediately revoke their access</li>
                        <li>Log them out of all sessions</li>
                        <li>Prevent them from logging in</li>
                        <li>Preserve their data for future reactivation</li>
                      </ul>
                    </>
                  )}
                </AlertDescription>
              </div>
            </Alert>

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Confirmation */}
            <div className="p-4 border rounded-lg bg-gray-50">
              <div className="text-sm font-medium text-gray-900 mb-2">
                Are you sure you want to {actionText.toLowerCase()} this user?
              </div>
              <div className="text-sm text-gray-600">
                {isActivating 
                  ? 'The user will be able to access the system immediately after activation.'
                  : 'This action will take effect immediately and the user will be logged out.'
                }
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleStatusUpdate(!user.active)}
                disabled={loading}
                variant={isActivating ? "default" : "destructive"}
                className={isActivating ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {isActivating ? 'Activating...' : 'Deactivating...'}
                  </>
                ) : (
                  <>
                    {isActivating ? (
                      <UserCheck className="h-4 w-4 mr-2" />
                    ) : (
                      <UserX className="h-4 w-4 mr-2" />
                    )}
                    {actionText} User
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
