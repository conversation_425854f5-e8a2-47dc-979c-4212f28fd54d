'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { X, Shield, User as UserIcon, Check } from 'lucide-react';
import { User } from '@/lib/user-service';

interface UserRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  availableRoles: string[];
  onUpdate: (userId: string, roles: string[]) => Promise<void>;
}

export default function UserRoleModal({ 
  isOpen, 
  onClose, 
  user, 
  availableRoles, 
  onUpdate 
}: UserRoleModalProps) {
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Initialize selected roles when modal opens
  useEffect(() => {
    if (isOpen && user) {
      setSelectedRoles([...user.roles]);
      setError('');
    }
  }, [isOpen, user]);

  // Handle role toggle
  const toggleRole = (role: string) => {
    setSelectedRoles(prev => {
      if (prev.includes(role)) {
        return prev.filter(r => r !== role);
      } else {
        return [...prev, role];
      }
    });
  };

  // Handle save
  const handleSave = async () => {
    if (selectedRoles.length === 0) {
      setError('User must have at least one role');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await onUpdate(user.id, selectedRoles);
      onClose();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to update user roles');
    } finally {
      setLoading(false);
    }
  };

  // Get role description
  const getRoleDescription = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Full system access with all permissions';
      case 'moderator':
        return 'Can moderate content and manage users';
      case 'user':
        return 'Standard user with basic permissions';
      default:
        return 'Custom role with specific permissions';
    }
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Shield className="h-4 w-4 text-red-600" />;
      case 'moderator':
        return <Shield className="h-4 w-4 text-blue-600" />;
      case 'user':
        return <UserIcon className="h-4 w-4 text-gray-600" />;
      default:
        return <Shield className="h-4 w-4 text-gray-600" />;
    }
  };

  if (!isOpen) return null;

  const hasChanges = JSON.stringify(selectedRoles.sort()) !== JSON.stringify(user.roles.sort());

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
              <CardTitle className="text-xl">Manage User Roles</CardTitle>
              <CardDescription>
                Update roles for {user.name}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* User Info */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex h-10 w-10 shrink-0 overflow-hidden rounded-full">
                {user.imageUrl ? (
                  <img 
                    src={user.imageUrl} 
                    alt={user.name} 
                    className="aspect-square h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center rounded-full bg-blue-100 text-blue-600 text-sm font-semibold">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium text-gray-900">{user.name}</div>
                <div className="text-sm text-gray-500">{user.email}</div>
              </div>
            </div>

            {/* Current Roles */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Current Roles</h4>
              <div className="flex flex-wrap gap-2">
                {user.roles.map((role) => (
                  <Badge key={role} variant="outline" className="flex items-center gap-1">
                    {getRoleIcon(role)}
                    {role}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Available Roles */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Select Roles</h4>
              <div className="space-y-2">
                {availableRoles.map((role) => {
                  const isSelected = selectedRoles.includes(role);
                  return (
                    <div
                      key={role}
                      onClick={() => toggleRole(role)}
                      className={`
                        p-3 border rounded-lg cursor-pointer transition-colors
                        ${isSelected 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getRoleIcon(role)}
                          <div>
                            <div className="font-medium text-gray-900">{role}</div>
                            <div className="text-xs text-gray-500">
                              {getRoleDescription(role)}
                            </div>
                          </div>
                        </div>
                        {isSelected && (
                          <Check className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Selected Roles Preview */}
            {selectedRoles.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Roles</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedRoles.map((role) => (
                    <Badge key={role} variant="default" className="flex items-center gap-1">
                      {getRoleIcon(role)}
                      {role}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={loading || !hasChanges || selectedRoles.length === 0}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  'Update Roles'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
