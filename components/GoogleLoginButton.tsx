'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import authService from '@/lib/auth-service';

export default function GoogleLoginButton() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleGoogleLogin = async () => {
    try {
      setLoading(true);

      // Try to get Google OAuth URL from backend first
      try {
        const authUrl = await authService.getGoogleAuthUrl();
        console.log('Got Google auth URL from backend:', authUrl);
        window.location.href = authUrl;
        return;
      } catch (backendError) {
        console.warn('Backend Google URL failed, using direct Google OAuth:', backendError);
      }

      // Fallback: Direct Google OAuth URL construction
      // Use backend redirect URI since Google will callback to backend first
      const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '376968074500-j0h7ldkq77gpe46dhtl2hjjnk97gnir5.apps.googleusercontent.com';
      const redirectUri = 'http://localhost:8080/auth-service/api/v1/oauth2/callback/google';
      const scope = encodeURIComponent('email profile');
      const responseType = 'code';
      const state = Math.random().toString(36).substring(2, 15);

      console.log('OAuth Configuration:', {
        clientId: googleClientId,
        redirectUri: redirectUri,
        scope: 'email profile',
        responseType: responseType,
        state: state
      });

      const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${googleClientId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=${scope}&` +
        `response_type=${responseType}&` +
        `state=${state}&` +
        `access_type=offline&` +
        `prompt=consent`;

      console.log('Full Google OAuth URL:', googleAuthUrl);
      console.log('Redirecting to Google OAuth (via backend)...');
      window.location.href = googleAuthUrl;

    } catch (error) {
      console.error('Google login error:', error);
      setLoading(false);
    }
  };

  return (
    <Button
      type="button"
      variant="outline"
      onClick={handleGoogleLogin}
      disabled={loading}
      className="w-full flex items-center justify-center gap-2 border-gray-300 hover:bg-gray-50"
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <svg className="h-5 w-5" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      )}
      <span>{loading ? 'Connecting...' : 'Continue with Google'}</span>
    </Button>
  );
}
