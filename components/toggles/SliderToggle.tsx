import { motion } from "framer-motion";
import { Dispatch, SetStateAction, useState } from "react";
import { FiMoon, FiSun } from "react-icons/fi";

const TOGGLE_CLASSES =
  "text-sm font-medium flex items-center gap-2 px-3 md:pl-3 md:pr-3.5 py-3 md:py-1.5 transition-colors relative z-10";

type ToggleOptionsType = "light" | "dark";

interface SliderToggleProps {
  selected?: ToggleOptionsType;
  setSelected?: Dispatch<SetStateAction<ToggleOptionsType>>;
  onChange?: (value: ToggleOptionsType) => void;
  className?: string;
  disabled?: boolean;
  variant?: "violet" | "blue" | "green" | "red";
  labels?: {
    light: string;
    dark: string;
  };
  icons?: {
    light: React.ReactNode;
    dark: React.ReactNode;
  };
}

const SliderToggle = ({
  selected: controlledSelected,
  setSelected: controlledSetSelected,
  onChange,
  className = "",
  disabled = false,
  variant = "violet",
  labels = { light: "Light", dark: "Dark" },
  icons = { light: <FiMoon className="relative z-10 text-lg md:text-sm" />, dark: <FiSun className="relative z-10 text-lg md:text-sm" /> }
}: SliderToggleProps) => {
  const [internalSelected, setInternalSelected] = useState<ToggleOptionsType>("light");
  
  const selected = controlledSelected || internalSelected;
  const setSelected = controlledSetSelected || setInternalSelected;

  const handleToggle = (value: ToggleOptionsType) => {
    if (disabled) return;
    
    setSelected(value);
    if (onChange) {
      onChange(value);
    }
  };

  const getGradientColors = () => {
    switch (variant) {
      case "blue":
        return "from-blue-600 to-cyan-600";
      case "green":
        return "from-green-600 to-emerald-600";
      case "red":
        return "from-red-600 to-pink-600";
      default:
        return "from-violet-600 to-indigo-600";
    }
  };

  return (
    <div className={`relative flex w-fit items-center rounded-full ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}>
      <button
        className={`${TOGGLE_CLASSES} ${
          selected === "light" ? "text-white" : "text-slate-300"
        }`}
        onClick={() => handleToggle("light")}
        disabled={disabled}
      >
        {icons.light}
        <span className="relative z-10">{labels.light}</span>
      </button>
      <button
        className={`${TOGGLE_CLASSES} ${
          selected === "dark" ? "text-white" : "text-slate-800"
        }`}
        onClick={() => handleToggle("dark")}
        disabled={disabled}
      >
        {icons.dark}
        <span className="relative z-10">{labels.dark}</span>
      </button>
      <div
        className={`absolute inset-0 z-0 flex ${
          selected === "dark" ? "justify-end" : "justify-start"
        }`}
      >
        <motion.span
          layout
          transition={{ type: "spring", damping: 15, stiffness: 250 }}
          className={`h-full w-1/2 rounded-full bg-gradient-to-r ${getGradientColors()}`}
        />
      </div>
    </div>
  );
};

export default SliderToggle;
