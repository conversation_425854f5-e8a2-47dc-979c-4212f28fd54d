'use client';

import React, { useState, DragEvent } from 'react';

interface SimpleDragDropProps {
  children: React.ReactNode;
  onReorder: (fromIndex: number, toIndex: number) => void;
  className?: string;
}

export function SimpleDragDrop({ children, onReorder, className = '' }: SimpleDragDropProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleDragStart = (e: DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', index.toString());
  };

  const handleDragOver = (e: DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: DragEvent, toIndex: number) => {
    e.preventDefault();
    const fromIndex = parseInt(e.dataTransfer.getData('text/html'));
    
    if (fromIndex !== toIndex && draggedIndex !== null) {
      onReorder(fromIndex, toIndex);
    }
    
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const childrenArray = React.Children.toArray(children);

  return (
    <div className={className}>
      {childrenArray.map((child, index) => (
        <div
          key={index}
          draggable
          onDragStart={(e) => handleDragStart(e, index)}
          onDragOver={(e) => handleDragOver(e, index)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, index)}
          onDragEnd={handleDragEnd}
          className={`
            ${draggedIndex === index ? 'opacity-50' : ''}
            ${dragOverIndex === index ? 'border-t-2 border-blue-500' : ''}
            transition-opacity duration-200
          `}
        >
          {child}
        </div>
      ))}
    </div>
  );
}

interface DraggableItemProps {
  children: React.ReactNode;
  className?: string;
}

export function DraggableItem({ children, className = '' }: DraggableItemProps) {
  return (
    <div className={`cursor-move ${className}`}>
      {children}
    </div>
  );
}
