'use client';

import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, <PERSON>, Eye, MousePointer, Bell } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { LanguageCode } from '@/lib/types/category';

interface BulkNotification {
  id: string;
  title: string;
  message: string;
  status: string;
  priority: string;
  createdAt: string;
  targetUserCount: number;
  readCount: number;
  clickedCount: number;
  language?: string;
}

export default function RecentNotifications() {
  const [notifications, setNotifications] = useState<BulkNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [language] = useState<LanguageCode>(LanguageCode.EN); // Default to English

  useEffect(() => {
    fetchRecentNotifications();
  }, [language]);

  const fetchRecentNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/notification-service/api/admin/notifications/dashboard/recent?page=0&size=10&language=${language}`);

      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data);
    } catch (err) {
      console.error('Error fetching recent notifications:', err);
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'sent':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-red-600 mb-2">{error}</p>
        <Button onClick={fetchRecentNotifications} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Bell className="h-12 w-12 mx-auto mb-3 text-gray-300" />
        <p>No recent notifications</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="flex-shrink-0 mt-1">
            <div className={`h-2 w-2 rounded-full ${
              notification.status.toLowerCase() === 'sent' ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  {notification.title}
                </h4>
                <p className="text-xs text-gray-600 mb-2 whitespace-pre-wrap">
                  {notification.message}
                </p>
                
                <div className="flex items-center space-x-3 text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-3 w-3" />
                    <span>{notification.targetUserCount} users</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{notification.readCount} read</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MousePointer className="h-3 w-3" />
                    <span>{notification.clickedCount} clicked</span>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col space-y-1 ml-3">
                <Badge className={getStatusColor(notification.status)} variant="outline">
                  {notification.status}
                </Badge>
                <Badge className={getPriorityColor(notification.priority)} variant="outline">
                  {notification.priority}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      <div className="pt-2 border-t">
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={() => window.location.href = '/notifications'}
        >
          View All Notifications
        </Button>
      </div>
    </div>
  );
}
