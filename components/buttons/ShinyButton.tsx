import { useEffect, useRef, useState } from "react";

interface ShinyButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

const ShinyButton = ({ 
  children = "Sign up free", 
  onClick,
  className = "",
  disabled = false
}: ShinyButtonProps) => {
  const parentRef = useRef<HTMLDivElement | null>(null);
  const btnRef = useRef<HTMLButtonElement | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const button = btnRef.current;
    const parent = parentRef.current;
    
    if (!button || !parent || disabled) return;

    const handleMouseOver = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    const handleMouseMove = (e: MouseEvent) => {
      const rect = button.getBoundingClientRect();
      setMousePos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    };

    button.addEventListener("mouseover", handleMouseOver);
    button.addEventListener("mouseleave", handleMouseLeave);
    button.addEventListener("mousemove", handleMouseMove);

    return () => {
      button.removeEventListener("mouseover", handleMouseOver);
      button.removeEventListener("mouseleave", handleMouseLeave);
      button.removeEventListener("mousemove", handleMouseMove);
    };
  }, [disabled]);

  return (
    <div ref={parentRef} className="relative">
      <button
        ref={btnRef}
        onClick={onClick}
        disabled={disabled}
        className={`
          relative overflow-hidden font-mono cursor-pointer text-white rounded px-4 py-2 
          bg-gradient-to-r from-purple-500 to-indigo-600
          transition-all duration-150 ease-in-out
          shadow-md hover:shadow-lg hover:-translate-y-0.5 
          active:translate-y-0.5 active:shadow-inner
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${className}
        `}
      >
        {/* Shine effect */}
        <div 
          className={`
            absolute inset-0 rounded
            transition-all duration-300 ease-out
            ${isHovered ? 'opacity-30' : 'opacity-0'}
          `}
          style={{
            background: `radial-gradient(${isHovered ? '250px' : '0px'} circle at ${mousePos.x}px ${mousePos.y}px, rgba(255, 255, 255, 0.3), transparent 70%)`
          }}
        />
        
        {/* Content */}
        <span className="relative z-10">
          {children}
        </span>
      </button>
    </div>
  );
};

export default ShinyButton;
