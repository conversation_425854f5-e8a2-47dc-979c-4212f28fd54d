import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import { IconType } from "react-icons";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiX } from "react-icons/fi";

interface LoadAndErrorButtonProps {
  children?: React.ReactNode;
  onClick?: () => Promise<void> | void;
  className?: string;
  disabled?: boolean;
  loadingText?: string;
  successText?: string;
  errorText?: string;
}

const LoadAndErrorButton = ({
  children = "Click Me and Wait",
  onClick,
  className = "",
  disabled = false,
  loadingText,
  successText,
  errorText
}: LoadAndErrorButtonProps) => {
  const [variant, setVariant] = useState<
    "neutral" | "loading" | "error" | "success"
  >("neutral");

  const classNames =
    variant === "neutral"
      ? "bg-indigo-500 hover:bg-indigo-600"
      : variant === "error"
        ? "bg-red-500"
        : variant === "success"
          ? "bg-green-500"
          : "bg-indigo-300 pointer-events-none";

  const handleClick = async () => {
    if (variant !== "neutral" || disabled) return;

    setVariant("loading");
    
    try {
      if (onClick) {
        await onClick();
      }
      setVariant("success");
    } catch (error) {
      setVariant("error");
    }

    setTimeout(() => {
      setVariant("neutral");
    }, 2500);
  };

  const getDisplayText = () => {
    switch (variant) {
      case "loading":
        return loadingText || children;
      case "success":
        return successText || children;
      case "error":
        return errorText || children;
      default:
        return children;
    }
  };

  return (
    <motion.button
      disabled={variant !== "neutral" || disabled}
      onClick={handleClick}
      className={`relative rounded-md px-4 py-2 font-medium text-white transition-all ${classNames} ${className}`}
    >
      <motion.span
        animate={{
          y: variant === "neutral" ? 0 : 6,
          opacity: variant === "neutral" ? 1 : 0,
        }}
        className="inline-block"
      >
        {getDisplayText()}
      </motion.span>
      <IconOverlay Icon={FiLoader} visible={variant === "loading"} spin />
      <IconOverlay Icon={FiX} visible={variant === "error"} />
      <IconOverlay Icon={FiCheck} visible={variant === "success"} />
    </motion.button>
  );
};

const IconOverlay = ({
  Icon,
  visible,
  spin = false,
}: {
  Icon: IconType;
  visible: boolean;
  spin?: boolean;
}) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{
            y: -12,
            opacity: 0,
          }}
          animate={{
            y: 0,
            opacity: 1,
          }}
          exit={{
            y: 12,
            opacity: 0,
          }}
          className="absolute inset-0 grid place-content-center"
        >
          <Icon className={`text-xl duration-300 ${spin && "animate-spin"}`} />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadAndErrorButton;
