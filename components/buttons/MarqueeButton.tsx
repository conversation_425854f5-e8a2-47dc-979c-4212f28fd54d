import React from "react";
import { motion } from "framer-motion";

interface MarqueeButtonProps {
  children: string;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  variant?: "indigo" | "red" | "green" | "blue";
  speed?: number;
}

const MarqueeButton = ({ 
  children, 
  onClick,
  className = "",
  disabled = false,
  variant = "indigo",
  speed = 5
}: MarqueeButtonProps) => {
  const getVariantColors = () => {
    switch (variant) {
      case "red":
        return "bg-red-200 text-red-900";
      case "green":
        return "bg-green-200 text-green-900";
      case "blue":
        return "bg-blue-200 text-blue-900";
      default:
        return "bg-indigo-200 text-indigo-900";
    }
  };

  return (
    <motion.button
      whileHover={{
        scale: disabled ? 1 : 1.05,
      }}
      whileTap={{
        scale: disabled ? 1 : 0.95,
      }}
      onClick={onClick}
      disabled={disabled}
      className={`relative overflow-hidden rounded-full p-4 text-xl font-black uppercase ${getVariantColors()} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
    >
      <motion.span
        className="block"
        initial={{ x: "0%" }}
        animate={{
          x: disabled ? "0%" : "calc(-100% - 6px)",
        }}
        transition={{
          ease: "linear",
          duration: speed,
          repeat: disabled ? 0 : Infinity,
          repeatType: "loop",
        }}
      >
        {children} •{" "}
      </motion.span>

      <motion.span
        initial={{ x: "calc(-100% - 6px)" }}
        animate={{
          x: disabled ? "calc(-100% - 6px)" : "calc(-200% - 12px)",
        }}
        transition={{
          ease: "linear",
          duration: speed,
          repeat: disabled ? 0 : Infinity,
          repeatType: "loop",
        }}
        className="absolute left-4 top-4 block"
      >
        {children} •
      </motion.span>
      <motion.span
        initial={{ x: "calc(100% + 6px)" }}
        animate={{
          x: disabled ? "calc(100% + 6px)" : "0%",
        }}
        transition={{
          ease: "linear",
          duration: speed,
          repeat: disabled ? 0 : Infinity,
          repeatType: "loop",
        }}
        className="absolute left-4 top-4 block"
      >
        {children} •
      </motion.span>
      <motion.span
        initial={{ x: "calc(200% + 12px)" }}
        animate={{
          x: disabled ? "calc(200% + 12px)" : "calc(100% + 6px)",
        }}
        transition={{
          ease: "linear",
          duration: speed,
          repeat: disabled ? 0 : Infinity,
          repeatType: "loop",
        }}
        className="absolute left-4 top-4 block"
      >
        {children} •
      </motion.span>
    </motion.button>
  );
};

export default MarqueeButton;
