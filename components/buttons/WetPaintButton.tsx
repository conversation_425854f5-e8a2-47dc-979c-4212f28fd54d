import { motion } from "framer-motion";

interface WetPaintButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  variant?: "violet" | "blue" | "green" | "red";
}

interface DripProps {
  left: string;
  height: number;
  delay: number;
  color: string;
}

const WetPaintButton = ({
  children = "Wet Paint Button",
  onClick,
  className = "",
  disabled = false,
  variant = "violet"
}: WetPaintButtonProps) => {
  const getVariantColors = () => {
    switch (variant) {
      case "blue":
        return "bg-blue-500 hover:bg-blue-600";
      case "green":
        return "bg-green-500 hover:bg-green-600";
      case "red":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-violet-500 hover:bg-violet-600";
    }
  };

  const getDripColor = () => {
    switch (variant) {
      case "blue":
        return "blue-500";
      case "green":
        return "green-500";
      case "red":
        return "red-500";
      default:
        return "violet-500";
    }
  };

  const colors = getVariantColors();
  const dripColor = getDripColor();

  return (
    <button 
      className={`group relative rounded px-4 py-2.5 font-semibold text-white transition-colors ${colors} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
      {!disabled && (
        <>
          <Drip left="10%" height={24} delay={0.5} color={dripColor} />
          <Drip left="30%" height={20} delay={3} color={dripColor} />
          <Drip left="57%" height={10} delay={4.25} color={dripColor} />
          <Drip left="85%" height={16} delay={1.5} color={dripColor} />
        </>
      )}
    </button>
  );
};

const Drip = ({ left, height, delay, color }: DripProps) => {
  return (
    <motion.div
      className="absolute top-[99%] origin-top"
      style={{
        left,
      }}
      initial={{ scaleY: 0.75 }}
      animate={{ scaleY: [0.75, 1, 0.75] }}
      transition={{
        duration: 2,
        times: [0, 0.25, 1],
        delay,
        ease: "easeIn",
        repeat: Infinity,
        repeatDelay: 2,
      }}
    >
      <div
        style={{ height }}
        className={`w-2 rounded-b-full bg-${color} transition-colors group-hover:bg-${color.replace('500', '600')}`}
      />
      <svg
        width="6"
        height="6"
        viewBox="0 0 6 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="absolute left-full top-0"
      >
        <g clipPath="url(#clip0_1077_28)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.4 0H0V5.4C0 2.41765 2.41766 0 5.4 0Z"
            className={`fill-${color} transition-colors group-hover:fill-${color.replace('500', '600')}`}
          />
        </g>
        <defs>
          <clipPath id="clip0_1077_28">
            <rect width="6" height="6" fill="white" />
          </clipPath>
        </defs>
      </svg>
      <svg
        width="6"
        height="6"
        viewBox="0 0 6 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="absolute right-full top-0 rotate-90"
      >
        <g clipPath="url(#clip0_1077_28)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.4 0H0V5.4C0 2.41765 2.41766 0 5.4 0Z"
            className={`fill-${color} transition-colors group-hover:fill-${color.replace('500', '600')}`}
          />
        </g>
        <defs>
          <clipPath id="clip0_1077_28">
            <rect width="6" height="6" fill="white" />
          </clipPath>
        </defs>
      </svg>

      <motion.div
        initial={{ y: -8, opacity: 1 }}
        animate={{ y: [-8, 50], opacity: [1, 0] }}
        transition={{
          duration: 2,
          times: [0, 1],
          delay,
          ease: "easeIn",
          repeat: Infinity,
          repeatDelay: 2,
        }}
        className={`absolute top-full h-2 w-2 rounded-full bg-${color} transition-colors group-hover:bg-${color.replace('500', '600')}`}
      />
    </motion.div>
  );
};

export default WetPaintButton;
