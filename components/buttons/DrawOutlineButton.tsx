import React from "react";

interface DrawOutlineButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "indigo" | "red" | "green" | "blue";
}

const DrawOutlineButton = ({
  children,
  variant = "indigo",
  className = "",
  ...rest
}: DrawOutlineButtonProps) => {
  const getVariantColors = () => {
    switch (variant) {
      case "red":
        return "hover:text-red-300 bg-red-300";
      case "green":
        return "hover:text-green-300 bg-green-300";
      case "blue":
        return "hover:text-blue-300 bg-blue-300";
      default:
        return "hover:text-indigo-300 bg-indigo-300";
    }
  };

  const colors = getVariantColors();

  return (
    <button
      {...rest}
      className={`group relative px-4 py-2 font-medium text-slate-100 transition-colors duration-[400ms] ${colors.split(' ')[0]} ${className}`}
    >
      <span>{children}</span>

      {/* TOP */}
      <span className={`absolute left-0 top-0 h-[2px] w-0 ${colors.split(' ')[1]} transition-all duration-100 group-hover:w-full`} />

      {/* RIGHT */}
      <span className={`absolute right-0 top-0 h-0 w-[2px] ${colors.split(' ')[1]} transition-all delay-100 duration-100 group-hover:h-full`} />

      {/* BOTTOM */}
      <span className={`absolute bottom-0 right-0 h-[2px] w-0 ${colors.split(' ')[1]} transition-all delay-200 duration-100 group-hover:w-full`} />

      {/* LEFT */}
      <span className={`absolute bottom-0 left-0 h-0 w-[2px] ${colors.split(' ')[1]} transition-all delay-300 duration-100 group-hover:h-full`} />
    </button>
  );
};

export default DrawOutlineButton;
