'use client';

import React from 'react';
import { ErrorNotification } from './ErrorNotification';
import { FieldError, InlineError } from './ErrorNotification';
import { ErrorResponse } from '@/lib/types/common';
import { isValidationError, formatFieldErrors } from '@/lib/utils/error-utils';

interface FormErrorHandlerProps {
  error?: ErrorResponse | null;
  onRetry?: () => void;
  onClose?: () => void;
  className?: string;
}

export const FormErrorHandler: React.FC<FormErrorHandlerProps> = ({
  error,
  onRetry,
  onClose,
  className = 'mb-4'
}) => {
  if (!error) return null;

  return (
    <ErrorNotification
      type="error"
      message={error.message}
      error={error}
      onRetry={onRetry}
      onClose={onClose}
      showRetry={!!onRetry}
      className={className}
    />
  );
};

// Hook for form error handling
export const useFormErrorHandler = () => {
  const [formError, setFormError] = React.useState<ErrorResponse | null>(null);
  const [fieldErrors, setFieldErrors] = React.useState<Record<string, string>>({});

  const handleFormError = React.useCallback((error: unknown) => {
    if (isValidationError(error) && error instanceof Error) {
      const axiosError = error as any;
      const errorData = axiosError.response?.data;
      
      if (errorData?.fieldErrors) {
        setFieldErrors(errorData.fieldErrors);
        setFormError({
          status: 400,
          error: 'Validation Error',
          message: 'Please check the form fields and try again',
          fieldErrors: errorData.fieldErrors,
          timestamp: new Date().toISOString()
        });
      } else {
        setFormError({
          status: 400,
          error: 'Validation Error',
          message: errorData?.message || 'Please check your input and try again',
          timestamp: new Date().toISOString()
        });
      }
    } else {
      // General error
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setFormError({
        status: 500,
        error: 'Error',
        message: errorMessage,
        timestamp: new Date().toISOString()
      });
      setFieldErrors({});
    }
  }, []);

  const clearErrors = React.useCallback(() => {
    setFormError(null);
    setFieldErrors({});
  }, []);

  const clearFieldError = React.useCallback((fieldName: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  const getFieldError = React.useCallback((fieldName: string) => {
    return fieldErrors[fieldName];
  }, [fieldErrors]);

  const hasFieldError = React.useCallback((fieldName: string) => {
    return !!fieldErrors[fieldName];
  }, [fieldErrors]);

  return {
    formError,
    fieldErrors,
    handleFormError,
    clearErrors,
    clearFieldError,
    getFieldError,
    hasFieldError
  };
};

// Form field wrapper with error handling
interface FormFieldProps {
  children: React.ReactNode;
  error?: string;
  touched?: boolean;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  children,
  error,
  touched = true,
  className = ''
}) => {
  return (
    <div className={className}>
      {children}
      <FieldError error={error} touched={touched} />
    </div>
  );
};

// Form section with error boundary
interface FormSectionProps {
  children: React.ReactNode;
  title?: string;
  error?: ErrorResponse | null;
  onRetry?: () => void;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  children,
  title,
  error,
  onRetry,
  className = ''
}) => {
  return (
    <div className={className}>
      {title && (
        <h3 className="text-lg font-medium mb-4">{title}</h3>
      )}
      
      <FormErrorHandler 
        error={error} 
        onRetry={onRetry}
        onClose={() => {}} 
      />
      
      {children}
    </div>
  );
};

// Validation error summary component
interface ValidationErrorSummaryProps {
  fieldErrors: Record<string, string>;
  onClose?: () => void;
  className?: string;
}

export const ValidationErrorSummary: React.FC<ValidationErrorSummaryProps> = ({
  fieldErrors,
  onClose,
  className = 'mb-4'
}) => {
  if (!fieldErrors || Object.keys(fieldErrors).length === 0) {
    return null;
  }

  const errorCount = Object.keys(fieldErrors).length;
  const errorMessage = `Please fix ${errorCount} error${errorCount > 1 ? 's' : ''} below:`;

  return (
    <ErrorNotification
      type="error"
      title="Validation Errors"
      message={errorMessage}
      error={{
        status: 400,
        error: 'Validation Error',
        message: errorMessage,
        fieldErrors,
        timestamp: new Date().toISOString()
      }}
      onClose={onClose}
      className={className}
    />
  );
};
