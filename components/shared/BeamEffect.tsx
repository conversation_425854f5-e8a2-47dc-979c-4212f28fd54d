'use client';

import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface BeamProps {
  top: number;
  left: number;
  delay?: number;
  duration?: number;
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo";
}

interface BeamEffectProps {
  beams?: BeamProps[];
  className?: string;
  autoGenerate?: boolean;
  beamCount?: number;
  containerHeight?: number;
  containerWidth?: number;
}

const colorClasses = {
  blue: "from-blue-500/0 to-blue-500",
  green: "from-green-500/0 to-green-500",
  purple: "from-purple-500/0 to-purple-500",
  red: "from-red-500/0 to-red-500",
  orange: "from-orange-500/0 to-orange-500",
  indigo: "from-indigo-500/0 to-indigo-500",
};

const Beam: React.FC<BeamProps> = ({ 
  top, 
  left, 
  delay = 0, 
  duration = 3,
  color = "blue"
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(true);
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div
      className={cn(
        "absolute z-10 h-16 w-[1px] bg-gradient-to-b transition-all",
        colorClasses[color],
        isAnimating ? "animate-pulse" : ""
      )}
      style={{
        top: `${top}px`,
        left: `${left}px`,
        animation: isAnimating 
          ? `beamMove ${duration}s ease-in-out infinite` 
          : 'none'
      }}
    />
  );
};

export const BeamEffect: React.FC<BeamEffectProps> = ({
  beams,
  className,
  autoGenerate = true,
  beamCount = 5,
  containerHeight = 400,
  containerWidth = 600
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [generatedBeams, setGeneratedBeams] = useState<BeamProps[]>([]);

  useEffect(() => {
    if (autoGenerate && !beams) {
      const newBeams: BeamProps[] = [];
      
      for (let i = 0; i < beamCount; i++) {
        newBeams.push({
          top: Math.random() * (containerHeight - 64),
          left: Math.random() * containerWidth,
          delay: Math.random() * 2,
          duration: 2 + Math.random() * 2,
          color: ['blue', 'green', 'purple', 'red', 'orange', 'indigo'][
            Math.floor(Math.random() * 6)
          ] as BeamProps['color']
        });
      }
      
      setGeneratedBeams(newBeams);
    }
  }, [autoGenerate, beams, beamCount, containerHeight, containerWidth]);

  const beamsToRender = beams || generatedBeams;

  return (
    <>
      <style jsx>{`
        @keyframes beamMove {
          0% {
            transform: translateY(0px);
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            transform: translateY(256px);
            opacity: 0;
          }
        }
      `}</style>
      
      <div
        ref={containerRef}
        className={cn(
          "relative overflow-hidden",
          className
        )}
        style={{
          height: containerHeight,
          width: containerWidth
        }}
      >
        {beamsToRender.map((beam, index) => (
          <Beam key={index} {...beam} />
        ))}
      </div>
    </>
  );
};
