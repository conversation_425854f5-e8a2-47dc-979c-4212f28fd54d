'use client';

import React from "react";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { AnimatedCard } from "./AnimatedCard";
import { BubbleButton } from "./BubbleButton";
import { GlowingChip } from "./GlowingChip";

interface PricingFeature {
  name: string;
  included: boolean;
}

interface PricingPlan {
  name: string;
  price: number;
  period: string;
  description: string;
  features: PricingFeature[];
  popular?: boolean;
  buttonText?: string;
  onSelect?: () => void;
}

interface PricingCardProps {
  plan: PricingPlan;
  className?: string;
}

interface PricingGridProps {
  plans: PricingPlan[];
  title?: string;
  subtitle?: string;
  className?: string;
}

export const PricingCard: React.FC<PricingCardProps> = ({ plan, className }) => {
  return (
    <AnimatedCard
      variant={plan.popular ? "gradient" : "default"}
      animation="scale"
      className={cn(
        "relative h-full",
        plan.popular && "border-2 border-indigo-500",
        className
      )}
    >
      {plan.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <GlowingChip color="indigo" size="sm" glowIntensity="high">
            Most Popular
          </GlowingChip>
        </div>
      )}

      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="text-center mb-6">
          <h3 className={cn(
            "text-xl font-bold mb-2",
            plan.popular ? "text-white" : "text-gray-900"
          )}>
            {plan.name}
          </h3>
          <div className="mb-2">
            <span className={cn(
              "text-4xl font-bold",
              plan.popular ? "text-white" : "text-gray-900"
            )}>
              ${plan.price}
            </span>
            <span className={cn(
              "text-sm ml-1",
              plan.popular ? "text-gray-300" : "text-gray-500"
            )}>
              /{plan.period}
            </span>
          </div>
          <p className={cn(
            "text-sm",
            plan.popular ? "text-gray-300" : "text-gray-600"
          )}>
            {plan.description}
          </p>
        </div>

        {/* Features */}
        <div className="flex-1 mb-6">
          <ul className="space-y-3">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-center">
                {feature.included ? (
                  <Check className={cn(
                    "h-4 w-4 mr-3 flex-shrink-0",
                    plan.popular ? "text-green-400" : "text-green-500"
                  )} />
                ) : (
                  <X className={cn(
                    "h-4 w-4 mr-3 flex-shrink-0",
                    plan.popular ? "text-red-400" : "text-red-500"
                  )} />
                )}
                <span className={cn(
                  "text-sm",
                  plan.popular ? "text-gray-300" : "text-gray-600",
                  !feature.included && "line-through opacity-50"
                )}>
                  {feature.name}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Button */}
        <BubbleButton
          variant={plan.popular ? "light" : "primary"}
          size="md"
          className="w-full"
          onClick={plan.onSelect}
        >
          {plan.buttonText || "Get Started"}
        </BubbleButton>
      </div>
    </AnimatedCard>
  );
};

export const PricingGrid: React.FC<PricingGridProps> = ({
  plans,
  title = "Choose Your Plan",
  subtitle = "Select the perfect plan for your needs",
  className
}) => {
  return (
    <section className={cn("py-12", className)}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subtitle}</p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <PricingCard key={index} plan={plan} />
          ))}
        </div>
      </div>
    </section>
  );
};
