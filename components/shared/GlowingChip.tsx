'use client';

import React from "react";
import { cn } from "@/lib/utils";

interface GlowingChipProps {
  children: React.ReactNode;
  className?: string;
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo" | "zinc";
  size?: "sm" | "md" | "lg";
  glowIntensity?: "low" | "medium" | "high";
}

const colorClasses = {
  blue: {
    bg: "bg-blue-900/20 border-blue-700",
    text: "font-bold text-black dark:text-blue-50",
    glow: "from-blue-500/0 via-blue-300 to-blue-500/0"
  },
  green: {
    bg: "bg-green-900/20 border-green-700",
    text: "font-bold text-black dark:text-green-50",
    glow: "from-green-500/0 via-green-300 to-green-500/0"
  },
  purple: {
    bg: "bg-purple-900/20 border-purple-700",
    text: "font-bold text-black dark:text-purple-50",
    glow: "from-purple-500/0 via-purple-300 to-purple-500/0"
  },
  red: {
    bg: "bg-red-900/20 border-red-700",
    text: "font-bold text-black dark:text-red-50",
    glow: "from-red-500/0 via-red-300 to-red-500/0"
  },
  orange: {
    bg: "bg-orange-900/20 border-orange-700",
    text: "font-bold text-black dark:text-orange-50",
    glow: "from-orange-500/0 via-orange-300 to-orange-500/0"
  },
  indigo: {
    bg: "bg-indigo-900/20 border-indigo-700",
    text: "font-bold text-black dark:text-indigo-50",
    glow: "from-indigo-500/0 via-indigo-300 to-indigo-500/0"
  },
  zinc: {
    bg: "bg-zinc-900/20 border-zinc-700",
    text: "font-bold text-black dark:text-zinc-50",
    glow: "from-zinc-500/0 via-zinc-300 to-zinc-500/0"
  }
};

const sizeClasses = {
  sm: "px-2 py-1 text-xs",
  md: "px-3 py-1.5 text-sm",
  lg: "px-4 py-2 text-base",
};

const glowClasses = {
  low: "h-[0.5px]",
  medium: "h-[1px]",
  high: "h-[1.5px]",
};

export const GlowingChip: React.FC<GlowingChipProps> = ({
  children,
  className,
  color = "zinc",
  size = "md",
  glowIntensity = "medium"
}) => {
  return (
    <span 
      className={cn(
        "relative z-10 inline-block rounded-full border transition-all duration-300 hover:scale-105",
        colorClasses[color].bg,
        colorClasses[color].text,
        sizeClasses[size],
        className
      )}
    >
      {children}
      <span 
        className={cn(
          "absolute bottom-0 left-3 right-3 bg-gradient-to-r",
          colorClasses[color].glow,
          glowClasses[glowIntensity]
        )}
      />
    </span>
  );
};
