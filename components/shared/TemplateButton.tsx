import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const templateButton = cva(["uppercase", "transition-colors", "font-medium"], {
  variants: {
    intent: {
      primary: ["bg-indigo-600", "hover:bg-indigo-700", "text-white"],
      secondary: ["bg-zinc-900", "hover:bg-zinc-700", "text-white"],
      outline: ["bg-white", "hover:bg-zinc-200", "border", "border-zinc-900", "text-zinc-900"],
      success: ["bg-green-600", "hover:bg-green-700", "text-white"],
      warning: ["bg-yellow-600", "hover:bg-yellow-700", "text-white"],
      danger: ["bg-red-600", "hover:bg-red-700", "text-white"],
    },
    size: {
      small: ["px-3", "py-1.5", "rounded-md", "text-sm"],
      medium: ["p-3", "rounded-lg", "text-base"],
      large: ["px-6", "py-4", "rounded-xl", "text-lg"],
    },
  },
  compoundVariants: [
    { intent: "primary", size: "medium", class: "uppercase" }
  ],
  defaultVariants: {
    intent: "primary",
    size: "medium",
  },
});

export interface TemplateButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof templateButton> {}

export const TemplateButton: React.FC<TemplateButtonProps> = ({
  className,
  intent,
  size,
  ...props
}) => (
  <button 
    className={cn(templateButton({ intent, size }), className)} 
    {...props} 
  />
);
