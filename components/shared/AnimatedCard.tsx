'use client';

import React, { CSSProperties, ReactNode, useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface AnimatedCardProps {
  className?: string;
  children?: ReactNode;
  style?: CSSProperties;
  variant?: "default" | "glass" | "gradient" | "dark";
  animation?: "blur" | "slide" | "scale" | "none";
  delay?: number;
  duration?: number;
}

const variantClasses = {
  default: "border border-gray-200 bg-white",
  glass: "border border-white/20 bg-white/10 backdrop-blur-sm",
  gradient: "border border-zinc-700 bg-gradient-to-br from-zinc-950/50 to-zinc-900/80",
  dark: "border border-zinc-700 bg-zinc-900"
};

const animationVariants = {
  blur: {
    initial: { filter: "blur(2px)", opacity: 0 },
    animate: { filter: "blur(0px)", opacity: 1 }
  },
  slide: {
    initial: { transform: "translateY(20px)", opacity: 0 },
    animate: { transform: "translateY(0px)", opacity: 1 }
  },
  scale: {
    initial: { transform: "scale(0.95)", opacity: 0 },
    animate: { transform: "scale(1)", opacity: 1 }
  },
  none: {
    initial: { opacity: 1 },
    animate: { opacity: 1 }
  }
};

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  className,
  children,
  style = {},
  variant = "default",
  animation = "blur",
  delay = 0.25,
  duration = 0.5
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  const animationStyle = animation !== 'none' ? {
    ...animationVariants[animation][isVisible ? 'animate' : 'initial'],
    transition: `all ${duration}s ease-in-out ${delay}s`
  } : {};

  return (
    <div
      ref={ref}
      style={{
        ...style,
        ...animationStyle
      }}
      className={cn(
        "relative h-full w-full overflow-hidden rounded-2xl p-6 transition-all duration-300 hover:shadow-lg",
        variantClasses[variant],
        className
      )}
    >
      {children}
    </div>
  );
};
