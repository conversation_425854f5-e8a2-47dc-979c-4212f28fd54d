import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface SectionHeadingProps {
  children: ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  align?: "left" | "center" | "right";
}

const sizeClasses = {
  sm: "text-2xl md:text-3xl",
  md: "text-3xl md:text-4xl",
  lg: "text-4xl md:text-5xl",
  xl: "text-4xl md:text-6xl",
};

const alignClasses = {
  left: "text-left",
  center: "text-center mx-auto",
  right: "text-right",
};

export const SectionHeading: React.FC<SectionHeadingProps> = ({ 
  children, 
  className,
  size = "lg",
  align = "center"
}) => {
  return (
    <h2 className={cn(
      "mb-4 max-w-4xl font-bold leading-[1.15]",
      sizeClasses[size],
      alignClasses[align],
      className
    )}>
      {children}
    </h2>
  );
};
