'use client';

import React, { useState } from "react";
import { cn } from "@/lib/utils";

interface InteractiveDotGridProps {
  width?: number;
  height?: number;
  className?: string;
  dotColor?: "gray" | "blue" | "green" | "purple" | "red" | "orange" | "indigo";
  hoverColor?: "gray" | "blue" | "green" | "purple" | "red" | "orange" | "indigo";
  size?: "sm" | "md" | "lg";
}

const dotColorClasses = {
  gray: "from-gray-700 to-gray-400",
  blue: "from-blue-700 to-blue-400",
  green: "from-green-700 to-green-400",
  purple: "from-purple-700 to-purple-400",
  red: "from-red-700 to-red-400",
  orange: "from-orange-700 to-orange-400",
  indigo: "from-indigo-700 to-indigo-400",
};

const hoverColorClasses = {
  gray: "group-hover:from-gray-500 group-hover:to-white",
  blue: "group-hover:from-blue-500 group-hover:to-white",
  green: "group-hover:from-green-500 group-hover:to-white",
  purple: "group-hover:from-purple-500 group-hover:to-white",
  red: "group-hover:from-red-500 group-hover:to-white",
  orange: "group-hover:from-orange-500 group-hover:to-white",
  indigo: "group-hover:from-indigo-500 group-hover:to-white",
};

const sizeClasses = {
  sm: { dot: "h-1 w-1", padding: "p-1" },
  md: { dot: "h-2 w-2", padding: "p-2" },
  lg: { dot: "h-3 w-3", padding: "p-3" },
};

export const InteractiveDotGrid: React.FC<InteractiveDotGridProps> = ({
  width = 15,
  height = 10,
  className,
  dotColor = "gray",
  hoverColor = "indigo",
  size = "md"
}) => {
  const [animatingDots, setAnimatingDots] = useState<Set<number>>(new Set());

  const handleDotClick = (clickedIndex: number) => {
    // Create ripple effect from clicked dot
    const newAnimatingDots = new Set<number>();
    
    // Calculate distance from clicked dot and animate with delay
    for (let i = 0; i < width; i++) {
      for (let j = 0; j < height; j++) {
        const currentIndex = i * height + j;
        const clickedRow = Math.floor(clickedIndex / height);
        const clickedCol = clickedIndex % height;
        const currentRow = i;
        const currentCol = j;
        
        const distance = Math.sqrt(
          Math.pow(currentRow - clickedRow, 2) + Math.pow(currentCol - clickedCol, 2)
        );
        
        setTimeout(() => {
          setAnimatingDots(prev => new Set([...prev, currentIndex]));
          
          // Remove animation after duration
          setTimeout(() => {
            setAnimatingDots(prev => {
              const newSet = new Set(prev);
              newSet.delete(currentIndex);
              return newSet;
            });
          }, 500);
        }, distance * 100);
      }
    }
  };

  const dots = [];
  let index = 0;

  for (let i = 0; i < width; i++) {
    for (let j = 0; j < height; j++) {
      const isAnimating = animatingDots.has(index);
      
      dots.push(
        <div
          key={`${i}-${j}`}
          className={cn(
            "group cursor-pointer rounded-full transition-all duration-300 hover:bg-gray-100",
            sizeClasses[size].padding
          )}
          data-index={index}
          onClick={() => handleDotClick(index)}
        >
          <div
            className={cn(
              "rounded-full bg-gradient-to-b opacity-50 transition-all duration-300",
              sizeClasses[size].dot,
              `bg-gradient-to-b ${dotColorClasses[dotColor]}`,
              hoverColorClasses[hoverColor],
              isAnimating && "scale-150 opacity-100"
            )}
            data-index={index}
          />
        </div>
      );
      index++;
    }
  }

  return (
    <div
      className={cn(
        "grid gap-1 select-none",
        className
      )}
      style={{ gridTemplateColumns: `repeat(${width}, 1fr)` }}
    >
      {dots}
    </div>
  );
};
