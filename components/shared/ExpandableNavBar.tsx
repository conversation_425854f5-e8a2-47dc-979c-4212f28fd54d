'use client';

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface NavLink {
  title: string;
  href?: string;
  sublinks?: { title: string; href: string }[];
}

interface ExpandableNavBarProps {
  links: NavLink[];
  children?: React.ReactNode;
  className?: string;
  logo?: React.ReactNode;
}

export const ExpandableNavBar: React.FC<ExpandableNavBarProps> = ({
  links,
  children,
  className,
  logo
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={cn("relative", className)}>
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex-shrink-0">
              {logo || (
                <div className="text-xl font-bold text-gray-900">
                  LookForX
                </div>
              )}
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                {links.map((link, index) => (
                  <NavItem key={index} link={link} />
                ))}
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                {isOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-white border-t border-gray-200"
            >
              <div className="px-2 pt-2 pb-3 space-y-1">
                {links.map((link, index) => (
                  <MobileNavItem key={index} link={link} />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>

      {/* Content */}
      {children}
    </div>
  );
};

const NavItem: React.FC<{ link: NavLink }> = ({ link }) => {
  const [isHovered, setIsHovered] = useState(false);

  if (!link.sublinks) {
    return (
      <a
        href={link.href}
        className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
      >
        {link.title}
      </a>
    );
  }

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <button className="flex items-center text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors">
        {link.title}
        <ChevronDown className="ml-1 h-4 w-4" />
      </button>

      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1"
          >
            {link.sublinks.map((sublink, index) => (
              <a
                key={index}
                href={sublink.href}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              >
                {sublink.title}
              </a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const MobileNavItem: React.FC<{ link: NavLink }> = ({ link }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!link.sublinks) {
    return (
      <a
        href={link.href}
        className="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
      >
        {link.title}
      </a>
    );
  }

  return (
    <div>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-base font-medium"
      >
        {link.title}
        <ChevronDown
          className={cn(
            "h-4 w-4 transition-transform",
            isExpanded && "rotate-180"
          )}
        />
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="pl-6"
          >
            {link.sublinks.map((sublink, index) => (
              <a
                key={index}
                href={sublink.href}
                className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900"
              >
                {sublink.title}
              </a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
