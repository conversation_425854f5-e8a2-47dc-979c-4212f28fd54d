'use client';

import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

type BubbleButtonProps = {
  children: ReactNode;
  className?: string;
  variant?: "dark" | "light" | "primary" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg";
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const variantClasses = {
  dark: {
    base: "border-zinc-700 bg-gradient-to-br from-zinc-800 to-zinc-950 text-zinc-50",
    hover: "hover:text-zinc-900 before:bg-zinc-100"
  },
  light: {
    base: "border-gray-300 bg-gradient-to-br from-gray-100 to-gray-200 text-gray-900",
    hover: "hover:text-white before:bg-gray-800"
  },
  primary: {
    base: "border-blue-600 bg-gradient-to-br from-blue-500 to-blue-700 text-white",
    hover: "hover:text-blue-900 before:bg-blue-100"
  },
  success: {
    base: "border-green-600 bg-gradient-to-br from-green-500 to-green-700 text-white",
    hover: "hover:text-green-900 before:bg-green-100"
  },
  warning: {
    base: "border-orange-600 bg-gradient-to-br from-orange-500 to-orange-700 text-white",
    hover: "hover:text-orange-900 before:bg-orange-100"
  },
  danger: {
    base: "border-red-600 bg-gradient-to-br from-red-500 to-red-700 text-white",
    hover: "hover:text-red-900 before:bg-red-100"
  }
};

const sizeClasses = {
  sm: "px-2 py-1 text-sm",
  md: "px-3 py-1.5 text-base",
  lg: "px-4 py-2 text-lg",
};

export const BubbleButton: React.FC<BubbleButtonProps> = ({ 
  children, 
  className, 
  variant = "dark",
  size = "md",
  ...rest 
}) => {
  return (
    <button
      className={cn(
        // Base styles
        `relative z-0 flex items-center justify-center gap-2 overflow-hidden whitespace-nowrap rounded-md 
        border transition-all duration-300
        
        before:absolute before:inset-0
        before:-z-10 before:translate-y-[200%]
        before:scale-[2.5]
        before:rounded-[100%] 
        before:transition-transform before:duration-500
        before:content-[""]

        hover:scale-105
        hover:before:translate-y-[0%]
        active:scale-100
        
        disabled:opacity-50 disabled:cursor-not-allowed
        disabled:hover:scale-100
        disabled:hover:before:translate-y-[200%]`,
        
        // Variant styles
        variantClasses[variant].base,
        variantClasses[variant].hover,
        
        // Size styles
        sizeClasses[size],
        
        className
      )}
      {...rest}
    >
      {children}
    </button>
  );
};
