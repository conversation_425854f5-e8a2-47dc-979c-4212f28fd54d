'use client';

import React from "react";
import { cn } from "@/lib/utils";

interface ChipProps {
  children: React.ReactNode;
  variant?: "default" | "outline" | "filled";
  size?: "sm" | "md" | "lg";
  color?: "gray" | "blue" | "green" | "purple" | "red" | "orange" | "indigo";
  className?: string;
  icon?: React.ReactNode;
  removable?: boolean;
  onRemove?: () => void;
}

const variantClasses = {
  default: {
    gray: "bg-gray-100 text-gray-800 border border-gray-200",
    blue: "bg-blue-100 text-blue-800 border border-blue-200",
    green: "bg-green-100 text-green-800 border border-green-200",
    purple: "bg-purple-100 text-purple-800 border border-purple-200",
    red: "bg-red-100 text-red-800 border border-red-200",
    orange: "bg-orange-100 text-orange-800 border border-orange-200",
    indigo: "bg-indigo-100 text-indigo-800 border border-indigo-200",
  },
  outline: {
    gray: "bg-transparent text-gray-600 border border-gray-300",
    blue: "bg-transparent text-blue-600 border border-blue-300",
    green: "bg-transparent text-green-600 border border-green-300",
    purple: "bg-transparent text-purple-600 border border-purple-300",
    red: "bg-transparent text-red-600 border border-red-300",
    orange: "bg-transparent text-orange-600 border border-orange-300",
    indigo: "bg-transparent text-indigo-600 border border-indigo-300",
  },
  filled: {
    gray: "bg-gray-600 text-white border border-gray-600",
    blue: "bg-blue-600 text-white border border-blue-600",
    green: "bg-green-600 text-white border border-green-600",
    purple: "bg-purple-600 text-white border border-purple-600",
    red: "bg-red-600 text-white border border-red-600",
    orange: "bg-orange-600 text-white border border-orange-600",
    indigo: "bg-indigo-600 text-white border border-indigo-600",
  }
};

const sizeClasses = {
  sm: "px-2 py-0.5 text-xs",
  md: "px-2 py-1 text-sm",
  lg: "px-3 py-1.5 text-base",
};

export const Chip: React.FC<ChipProps> = ({
  children,
  variant = "default",
  size = "md",
  color = "gray",
  className,
  icon,
  removable = false,
  onRemove
}) => {
  return (
    <span
      className={cn(
        "inline-flex items-center gap-1 rounded-full font-medium transition-colors",
        variantClasses[variant][color],
        sizeClasses[size],
        className
      )}
    >
      {icon && (
        <span className="flex-shrink-0">
          {icon}
        </span>
      )}
      <span>{children}</span>
      {removable && onRemove && (
        <button
          onClick={onRemove}
          className="flex-shrink-0 ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors"
          type="button"
        >
          <svg
            className="w-3 h-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </span>
  );
};
