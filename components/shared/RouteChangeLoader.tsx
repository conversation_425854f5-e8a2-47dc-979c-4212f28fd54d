"use client";
import React, { useEffect, useState } from "react";
import LoadingSpinner from "./LoadingSpinner";

const RouteChangeLoader: React.FC = () => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Listen to route change events
    const handleStart = () => setLoading(true);
    const handleComplete = () => setLoading(false);
    // next/navigation router does not have events, so we use window events as workaround
    window.addEventListener("routeChangeStart", handleStart);
    window.addEventListener("routeChangeComplete", handleComplete);
    window.addEventListener("routeChangeError", handleComplete);
    return () => {
      window.removeEventListener("routeChangeStart", handleStart);
      window.removeEventListener("routeChangeComplete", handleComplete);
      window.removeEventListener("routeChangeError", handleComplete);
    };
  }, []);

  if (!loading) return null;
  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-white/80 dark:bg-black/80">
      <LoadingSpinner text="Loading..." />
    </div>
  );
};

export default RouteChangeLoader; 