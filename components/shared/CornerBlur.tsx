'use client';

import React from "react";
import { cn } from "@/lib/utils";

interface CornerBlurProps {
  className?: string;
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "all";
  size?: "sm" | "md" | "lg" | "xl";
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo" | "gray";
  intensity?: "low" | "medium" | "high";
}

const positionClasses = {
  "top-left": "top-0 left-0",
  "top-right": "top-0 right-0",
  "bottom-left": "bottom-0 left-0",
  "bottom-right": "bottom-0 right-0",
  "all": ""
};

const sizeClasses = {
  sm: "h-32 w-32",
  md: "h-48 w-48",
  lg: "h-64 w-64",
  xl: "h-80 w-80"
};

const colorClasses = {
  blue: "from-blue-400/30 to-blue-600/10",
  green: "from-green-400/30 to-green-600/10",
  purple: "from-purple-400/30 to-purple-600/10",
  red: "from-red-400/30 to-red-600/10",
  orange: "from-orange-400/30 to-orange-600/10",
  indigo: "from-indigo-400/30 to-indigo-600/10",
  gray: "from-gray-400/30 to-gray-600/10"
};

const intensityClasses = {
  low: "opacity-30",
  medium: "opacity-50",
  high: "opacity-70"
};

const BlurElement: React.FC<{
  position: string;
  size: string;
  color: string;
  intensity: string;
  className?: string;
}> = ({ position, size, color, intensity, className }) => (
  <div
    className={cn(
      "absolute rounded-full bg-gradient-radial blur-3xl",
      position,
      size,
      color,
      intensity,
      className
    )}
  />
);

export const CornerBlur: React.FC<CornerBlurProps> = ({
  className,
  position = "all",
  size = "md",
  color = "blue",
  intensity = "medium"
}) => {
  const positionClass = positionClasses[position];
  const sizeClass = sizeClasses[size];
  const colorClass = colorClasses[color];
  const intensityClass = intensityClasses[intensity];

  if (position === "all") {
    return (
      <div className={cn("absolute inset-0 overflow-hidden", className)}>
        <BlurElement
          position={positionClasses["top-left"]}
          size={sizeClass}
          color={colorClass}
          intensity={intensityClass}
        />
        <BlurElement
          position={positionClasses["top-right"]}
          size={sizeClass}
          color={colorClass}
          intensity={intensityClass}
        />
        <BlurElement
          position={positionClasses["bottom-left"]}
          size={sizeClass}
          color={colorClass}
          intensity={intensityClass}
        />
        <BlurElement
          position={positionClasses["bottom-right"]}
          size={sizeClass}
          color={colorClass}
          intensity={intensityClass}
        />
      </div>
    );
  }

  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      <BlurElement
        position={positionClass}
        size={sizeClass}
        color={colorClass}
        intensity={intensityClass}
      />
    </div>
  );
};
