'use client';

import React, { useState, useRef, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { AnimatedCard } from "./AnimatedCard";

interface FeatureItem {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo";
}

interface FeatureCarouselProps {
  features: FeatureItem[];
  title?: string;
  subtitle?: string;
  cardWidth?: number;
  className?: string;
}

const CARD_WIDTH = 350;
const MARGIN = 20;
const CARD_SIZE = CARD_WIDTH + MARGIN;

const BREAKPOINTS = {
  sm: 640,
  lg: 1024,
};

const colorClasses = {
  blue: "from-blue-400 to-blue-700 text-blue-50",
  green: "from-green-400 to-green-700 text-green-50",
  purple: "from-purple-400 to-purple-700 text-purple-50",
  red: "from-red-400 to-red-700 text-red-50",
  orange: "from-orange-400 to-orange-700 text-orange-50",
  indigo: "from-indigo-400 to-indigo-700 text-indigo-50",
};

export const FeatureCarousel: React.FC<FeatureCarouselProps> = ({
  features,
  title = "Features",
  subtitle = "Explore our amazing features",
  cardWidth = CARD_WIDTH,
  className
}) => {
  const [ref, setRef] = useState<HTMLDivElement | null>(null);
  const [width, setWidth] = useState(0);
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    if (ref) {
      const updateWidth = () => setWidth(ref.offsetWidth);
      updateWidth();
      window.addEventListener('resize', updateWidth);
      return () => window.removeEventListener('resize', updateWidth);
    }
  }, [ref]);

  const cardSize = cardWidth + MARGIN;
  const cardBuffer = width > BREAKPOINTS.lg ? 3 : width > BREAKPOINTS.sm ? 2 : 1;

  const canShiftLeft = offset < 0;
  const canShiftRight = Math.abs(offset) < cardSize * (features.length - cardBuffer);

  const shiftLeft = () => {
    if (!canShiftLeft) return;
    setOffset((prev) => prev + cardSize);
  };

  const shiftRight = () => {
    if (!canShiftRight) return;
    setOffset((prev) => prev - cardSize);
  };

  return (
    <section className={cn("relative overflow-hidden py-12", className)} ref={setRef}>
      <div className="relative z-20 overflow-hidden">
        <div className="mx-auto max-w-7xl px-4 md:px-8">
          {/* Header */}
          <div className="mb-12 flex flex-col items-center justify-between gap-6 md:flex-row md:items-end">
            <div className="space-y-3">
              <h2 className="text-3xl font-bold text-gray-900 md:text-4xl">{title}</h2>
              <p className="text-gray-600 md:text-lg">{subtitle}</p>
            </div>
            
            {/* Navigation */}
            <div className="flex items-center gap-2">
              <button
                className={cn(
                  "rounded-lg border border-gray-300 bg-white p-2 text-xl transition-all hover:bg-gray-50",
                  canShiftLeft ? "text-gray-700" : "text-gray-300 cursor-not-allowed"
                )}
                disabled={!canShiftLeft}
                onClick={shiftLeft}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button
                className={cn(
                  "rounded-lg border border-gray-300 bg-white p-2 text-xl transition-all hover:bg-gray-50",
                  canShiftRight ? "text-gray-700" : "text-gray-300 cursor-not-allowed"
                )}
                disabled={!canShiftRight}
                onClick={shiftRight}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Carousel */}
          <div
            className="grid transition-transform duration-300 ease-in-out"
            style={{
              gridTemplateColumns: `repeat(${features.length}, 1fr)`,
              transform: `translateX(${offset}px)`
            }}
          >
            {features.map((feature) => (
              <FeatureCard key={feature.id} feature={feature} width={cardWidth} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

const FeatureCard: React.FC<{ feature: FeatureItem; width: number }> = ({ 
  feature, 
  width 
}) => {
  return (
    <AnimatedCard
      className="shrink-0"
      variant="default"
      animation="blur"
      style={{
        width: width,
        marginRight: MARGIN,
      }}
    >
      <div 
        className={cn(
          "size-12 rounded-full bg-gradient-to-br p-3 mb-4",
          colorClasses[feature.color || 'blue']
        )}
      >
        {feature.icon}
      </div>
      <h3 className="mb-2 text-lg font-semibold text-gray-900">{feature.title}</h3>
      <p className="text-sm text-gray-600">{feature.description}</p>
    </AnimatedCard>
  );
};
