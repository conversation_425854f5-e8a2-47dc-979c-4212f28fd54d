'use client';

import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface RevealAnimationProps {
  children: React.ReactNode;
  width?: string;
  direction?: "up" | "down" | "left" | "right";
  delay?: number;
  duration?: number;
  slideColor?: "blue" | "green" | "purple" | "red" | "indigo" | "orange";
  className?: string;
}

const slideColorClasses = {
  blue: "bg-blue-500",
  green: "bg-green-500",
  purple: "bg-purple-500",
  red: "bg-red-500",
  indigo: "bg-indigo-500",
  orange: "bg-orange-500",
};

const directionVariants = {
  up: { 
    hidden: { opacity: 0, transform: "translateY(75px)" },
    visible: { opacity: 1, transform: "translateY(0px)" }
  },
  down: { 
    hidden: { opacity: 0, transform: "translateY(-75px)" },
    visible: { opacity: 1, transform: "translateY(0px)" }
  },
  left: { 
    hidden: { opacity: 0, transform: "translateX(75px)" },
    visible: { opacity: 1, transform: "translateX(0px)" }
  },
  right: { 
    hidden: { opacity: 0, transform: "translateX(-75px)" },
    visible: { opacity: 1, transform: "translateX(0px)" }
  }
};

const slideVariants = {
  up: { hidden: "translateY(0%)", visible: "translateY(-100%)" },
  down: { hidden: "translateY(0%)", visible: "translateY(100%)" },
  left: { hidden: "translateX(0%)", visible: "translateX(-100%)" },
  right: { hidden: "translateX(0%)", visible: "translateX(100%)" }
};

export const RevealAnimation: React.FC<RevealAnimationProps> = ({ 
  children, 
  width = "w-fit",
  direction = "up",
  delay = 0.25,
  duration = 0.5,
  slideColor = "indigo",
  className
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  return (
    <div 
      ref={ref} 
      className={cn(`relative overflow-hidden ${width}`, className)}
    >
      <div
        style={{
          opacity: isVisible ? 1 : 0,
          transform: isVisible 
            ? directionVariants[direction].visible.transform 
            : directionVariants[direction].hidden.transform,
          transition: `all ${duration}s ease-out ${delay}s`
        }}
      >
        {children}
      </div>
      
      {/* Sliding overlay */}
      <div
        className={cn(
          "absolute inset-0 z-20",
          slideColorClasses[slideColor]
        )}
        style={{
          transform: isVisible 
            ? slideVariants[direction].visible 
            : slideVariants[direction].hidden,
          transition: `transform ${duration}s ease-in ${delay * 0.5}s`
        }}
      />
    </div>
  );
};
