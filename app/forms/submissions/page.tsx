'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Download,
  Eye,
  Trash2,
  Calendar,
  User,
  FileText,
  MoreHorizontal,
  ArrowUpDown,

} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

import Layout from '@/components/layout/ModernLayout';
import { CategorySelector } from '@/components/categories/CategorySelector';
import { FormSelector } from '@/components/forms/FormSelector';
import { Label } from '@/components/ui/label';

interface FormSubmission {
  id: string;
  formTemplateId: number;
  categoryId: number;
  userId: number;
  requestId: string;
  submittedBy: string;
  responses: FormResponse[];
  createdAt: string;
  updatedAt?: string;
}

interface FormResponse {
  elementId: number;
  elementType: string;
  elementLabel: string;
  value: any;
  values?: any[];
}

interface FormTemplate {
  id: number;
  nameTranslations: Record<string, string>;
  categoryId: number;
}

interface Category {
  id: number;
  name: string;
  parentId?: number;
  type: string;
  level: number;
  fullPath: string;
  active: boolean;
}

interface SubmissionStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
}

export default function FormSubmissionsPage() {
  const router = useRouter();
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [formTemplates, setFormTemplates] = useState<FormTemplate[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [stats, setStats] = useState<SubmissionStats>({ total: 0, today: 0, thisWeek: 0, thisMonth: 0 });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedForm, setSelectedForm] = useState<number | null>(null);
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'form' | 'user'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);

  // Removed searchable dropdown states - using SearchableSelect component now

  const { toast } = useToast();

  useEffect(() => {
    fetchSubmissions();
    calculateStats();
  }, []);

  // Auto-fetch when filters change (except search term)
  useEffect(() => {
    fetchSubmissions(0); // Reset to first page when filter changes
  }, [selectedCategory, selectedForm, pageSize]);

  // Fetch submissions when page changes
  useEffect(() => {
    if (currentPage > 0) { // Don't fetch on initial load
      fetchSubmissions(currentPage);
    }
  }, [currentPage]);

  const fetchSubmissions = async (page = currentPage, search = searchTerm, categoryId = selectedCategory, formId = selectedForm) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        size: pageSize.toString(),
        sortBy: 'createdAt',
        sortDir: 'desc',
      });

      if (search && search.trim()) {
        params.append('search', search.trim());
      }

      if (categoryId) {
        params.append('categoryId', categoryId.toString());
      }

      if (formId) {
        params.append('formTemplateId', formId.toString());
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const url = `${API_BASE_URL}/question-form-service/api/v1/form-submissions?${params}`;
      console.log('Fetching submissions from:', url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();

        // Check if response is paginated or simple array
        if (data.content) {
          // Paginated response
          setSubmissions(data.content);
          setTotalPages(data.totalPages);
          setTotalElements(data.totalElements);
          setCurrentPage(data.number);
        } else {
          // Simple array response (fallback)
          setSubmissions(data);
          setTotalPages(1);
          setTotalElements(data.length);
          setCurrentPage(0);
        }
      } else {
        throw new Error('Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch form submissions',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };





  const calculateStats = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const todayCount = submissions.filter(s => new Date(s.createdAt) >= today).length;
    const weekCount = submissions.filter(s => new Date(s.createdAt) >= thisWeek).length;
    const monthCount = submissions.filter(s => new Date(s.createdAt) >= thisMonth).length;

    setStats({
      total: submissions.length,
      today: todayCount,
      thisWeek: weekCount,
      thisMonth: monthCount,
    });
  };

  const handleDeleteSubmission = async (id: string) => {
    if (!confirm('Are you sure you want to delete this submission?')) return;

    try {
      const response = await fetch(`/api/question-form-service/api/v1/form-submissions/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSubmissions(submissions.filter(s => s.id !== id));
        toast({
          title: 'Success',
          description: 'Submission deleted successfully',
        });
      } else {
        throw new Error('Failed to delete submission');
      }
    } catch (error) {
      console.error('Error deleting submission:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete submission',
        variant: 'destructive',
      });
    }
  };

  const exportSubmissions = async () => {
    try {
      // Export all submissions matching current filters (not just current page)
      const params = new URLSearchParams({
        unpaged: 'true',
        sortBy: 'createdAt',
        sortDir: 'desc',
      });

      if (searchTerm && searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      if (selectedCategory) {
        params.append('categoryId', selectedCategory.toString());
      }

      if (selectedForm) {
        params.append('formTemplateId', selectedForm.toString());
      }

      const response = await fetch(`/api/question-form-service/api/v1/form-submissions?${params}`);
      if (response.ok) {
        const data = await response.json();
        const allSubmissions = Array.isArray(data) ? data : data.content || [];

        const csvContent = generateCSV(allSubmissions);
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `form-submissions-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        toast({
          title: 'Success',
          description: `Exported ${allSubmissions.length} submissions successfully`,
        });
      } else {
        throw new Error('Failed to fetch submissions for export');
      }
    } catch (error) {
      console.error('Error exporting submissions:', error);
      toast({
        title: 'Error',
        description: 'Failed to export submissions',
        variant: 'destructive',
      });
    }
  };

  const generateCSV = (submissions: FormSubmission[]): string => {
    const headers = ['ID', 'Form', 'Category', 'User', 'Request ID', 'Submitted Date'];
    const rows = submissions.map(submission => [
      submission.id,
      getFormName(submission.formTemplateId),
      getCategoryName(submission.categoryId),
      submission.submittedBy,
      submission.requestId,
      new Date(submission.createdAt).toLocaleDateString(),
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const getFormName = (formTemplateId: number): string => {
    // Since we're using lazy loading, we can't get the form name here
    // This function is mainly used for display purposes
    return `Form ID: ${formTemplateId}`;
  };

  const handleSearch = () => {
    fetchSubmissions(0); // Reset to first page when searching
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    fetchSubmissions(0, ''); // Clear search and fetch
  };

  const getCategoryName = (categoryId: number): string => {
    const category = categories.find(c => c.id === categoryId);
    return category?.name || 'Unknown Category';
  };

  // Filtering and sorting is now done on the backend

  const handleSort = (field: 'date' | 'form' | 'user') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    // Trigger refetch with new sorting
    fetchSubmissions(0);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading submissions...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Form Submissions</h1>
            <p className="text-muted-foreground">
              Manage and analyze form submissions from users
            </p>
          </div>
          <Button onClick={exportSubmissions}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Today</p>
                  <p className="text-2xl font-bold">{stats.today}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">This Week</p>
                  <p className="text-2xl font-bold">{stats.thisWeek}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">This Month</p>
                  <p className="text-2xl font-bold">{stats.thisMonth}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="flex space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by user, request ID, or form name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      className="pl-10"
                    />
                  </div>
                  <Button onClick={handleSearch} disabled={loading}>
                    Search
                  </Button>
                  {searchTerm && (
                    <Button variant="outline" onClick={handleClearSearch}>
                      Clear
                    </Button>
                  )}
                </div>
              </div>
              <div className="min-w-[200px]">
                <CategorySelector
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  placeholder="Select category..."
                  searchPlaceholder="Search categories..."
                  className="w-full"
                  includeAllOption={true}
                  allOptionLabel="All Categories"
                />
              </div>
              <div className="min-w-[200px]">
                <FormSelector
                  value={selectedForm}
                  onChange={setSelectedForm}
                  placeholder="Select form..."
                  searchPlaceholder="Search forms..."
                  className="w-full"
                  includeAllOption={true}
                  allOptionLabel="All Forms"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submissions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Submissions ({totalElements})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request ID</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('form')}
                  >
                    <div className="flex items-center">
                      Form
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('user')}
                  >
                    <div className="flex items-center">
                      User
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Responses</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('date')}
                  >
                    <div className="flex items-center">
                      Submitted
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {submissions.map((submission) => (
                  <TableRow key={submission.id}>
                    <TableCell>
                      <div className="font-mono text-sm">{submission.requestId}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{getFormName(submission.formTemplateId)}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getCategoryName(submission.categoryId)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <User className="mr-2 h-4 w-4 text-gray-400" />
                        {submission.submittedBy}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {submission.responses.length} responses
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(submission.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(submission.createdAt).toLocaleTimeString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => router.push(`/forms/submissions/${submission.id}`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteSubmission(submission.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {submissions.length === 0 && !loading && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No submissions found</p>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalElements)} of {totalElements} submissions
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                      return (
                        <Button
                          key={pageNum}
                          variant={pageNum === currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                        >
                          {pageNum + 1}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                    disabled={currentPage >= totalPages - 1}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submission Details Dialog */}
        <Dialog open={!!selectedSubmission} onOpenChange={() => setSelectedSubmission(null)}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Submission Details</DialogTitle>
            </DialogHeader>
            {selectedSubmission && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Request ID</Label>
                    <p className="font-mono">{selectedSubmission.requestId}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Form</Label>
                    <p>{getFormName(selectedSubmission.formTemplateId)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Category</Label>
                    <p>{getCategoryName(selectedSubmission.categoryId)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Submitted By</Label>
                    <p>{selectedSubmission.submittedBy}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Submitted Date</Label>
                    <p>{new Date(selectedSubmission.createdAt).toLocaleString()}</p>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-500 mb-2 block">Responses</Label>
                  <div className="space-y-3">
                    {selectedSubmission.responses.map((response, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="font-medium text-sm mb-1">{response.elementLabel}</div>
                        <div className="text-sm text-gray-600">
                          Type: {response.elementType}
                        </div>
                        <div className="mt-2">
                          {Array.isArray(response.values) ? (
                            <div className="space-y-1">
                              {response.values.map((value, i) => (
                                <Badge key={i} variant="outline">{value}</Badge>
                              ))}
                            </div>
                          ) : (
                            <p className="bg-gray-50 p-2 rounded text-sm">
                              {response.value || 'No response'}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
}
