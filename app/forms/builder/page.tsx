'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Eye,       
  ArrowLeft,
  Plus,
  Trash2,
  GripVertical,
  Settings,
  Type,
  Hash,
  Mail,
  Calendar,
  CheckSquare,
  Circle,
  List,
  Upload,
  Star,
  Palette,
  ChevronUp,
  ChevronDown,
  X
} from 'lucide-react';
import type { DropResult } from '@hello-pangea/dnd';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/layout/ModernLayout';
import { LanguageCode } from '@/types/language';
import { ElementType, FormElement, ElementOption, Category, CreateFormTemplateRequest } from '@/types/form';
import { CategorySelector } from '@/components/categories/CategorySelector';
import { useSearchParams } from 'next/navigation';
import { ReferenceDataType } from '@/lib/types/reference-data';
import referenceDataService from '@/lib/reference-data-service';

// Types imported from @/types/form

const ELEMENT_TYPES = [
  { type: ElementType.TEXT, label: 'Text Input', icon: Type },
  { type: ElementType.TEXTAREA, label: 'Textarea', icon: Type },
  { type: ElementType.NUMBER, label: 'Number', icon: Hash },
  { type: ElementType.EMAIL, label: 'Email', icon: Mail },
  { type: ElementType.DATE, label: 'Date', icon: Calendar },
  { type: ElementType.CHECKBOX, label: 'Checkbox', icon: CheckSquare },
  { type: ElementType.CHECKBOX_GROUP, label: 'Checkbox Group', icon: CheckSquare },
  { type: ElementType.RADIO_GROUP, label: 'Radio Group', icon: Circle },
  { type: ElementType.SELECT, label: 'Select', icon: List },
  { type: ElementType.MULTI_SELECT, label: 'Multi Select', icon: List },
  { type: ElementType.FILE, label: 'File Upload', icon: Upload },
  { type: ElementType.RATING, label: 'Rating', icon: Star },
  { type: ElementType.COLOR, label: 'Color Picker', icon: Palette },
];

export default function FormBuilderPage() {
  const searchParams = useSearchParams();
  const editFormId = searchParams.get('edit');
  const isEditMode = !!editFormId;

  const [formNameTranslations, setFormNameTranslations] = useState<Record<string, string>>({});
  const [formDescriptionTranslations, setFormDescriptionTranslations] = useState<Record<string, string>>({});
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [categoryHasForm, setCategoryHasForm] = useState<boolean>(false);

  const [elements, setElements] = useState<FormElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<FormElement | null>(null);
  const [loading, setLoading] = useState(false);

  const [showPreview, setShowPreview] = useState(false);
  const [formLoading, setFormLoading] = useState(false); // Edit mode form loading
  const [formDataLoaded, setFormDataLoaded] = useState(!isEditMode); // Track if form data is loaded

  // Validation states
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isFormValid, setIsFormValid] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    if (isEditMode && editFormId) {
      loadFormForEdit(editFormId);
    }
  }, [isEditMode, editFormId]);

  // Load form for editing
  const loadFormForEdit = async (formId: string) => {
    setFormLoading(true);
    setFormDataLoaded(false);

    try {
      const response = await fetch(`/api/question-form-service/api/v1/form-templates/${formId}`);
      if (response.ok) {
        const formData = await response.json();
        console.log('Form loaded for editing:', formData);

        // Set form data
        setFormNameTranslations(formData.nameTranslations || {});
        setFormDescriptionTranslations(formData.descriptionTranslations || {});
        setSelectedCategory(formData.categoryId);
        setElements(formData.elements || []);

        // Mark form data as loaded
        setFormDataLoaded(true);

        toast({
          title: 'Success',
          description: 'Form loaded for editing',
        });
      } else {
        throw new Error('Failed to load form');
      }
    } catch (error) {
      console.error('Error loading form:', error);
      toast({
        title: 'Error',
        description: 'Failed to load form for editing',
        variant: 'destructive',
      });
      // Set as loaded even on error to prevent infinite loading
      setFormDataLoaded(true);
    } finally {
      setFormLoading(false);
    }
  };



  const generateId = () => {
    return Math.random().toString(36).substr(2, 9);
  };

  const needsOptions = (type: ElementType): boolean => {
    return [
      ElementType.CHECKBOX_GROUP,
      ElementType.RADIO_GROUP,
      ElementType.SELECT,
      ElementType.MULTI_SELECT
    ].includes(type);
  };

  // Validation functions
  const validateForm = useCallback(() => {
    const errors: Record<string, string> = {};

    // Validate form name
    const hasFormName = Object.values(formNameTranslations).some(name => name.trim());
    if (!hasFormName) {
      errors.formName = 'Form name is required in at least one language';
    }

    // Validate category
    if (!selectedCategory) {
      errors.category = 'Please select a category';
    }

    // Validate elements
    if (elements.length === 0) {
      errors.elements = 'At least one form element is required';
    }

    // Validate each element
    elements.forEach((element, index) => {
      const hasLabel = Object.values(element.labelTranslations || {}).some(label => label.trim());
      if (!hasLabel) {
        errors[`element_${index}_label`] = `Element ${index + 1}: Label is required in at least one language`;
      }

      // Validate options for elements that need them
      if (needsOptions(element.type) && (!element.options || element.options.length === 0)) {
        errors[`element_${index}_options`] = `Element ${index + 1}: At least one option is required`;
      }

      // Validate option labels
      element.options?.forEach((option, optionIndex) => {
        const hasOptionLabel = Object.values(option.labelTranslations || {}).some(label => label.trim());
        if (!hasOptionLabel) {
          errors[`element_${index}_option_${optionIndex}_label`] = `Element ${index + 1}, Option ${optionIndex + 1}: Label is required`;
        }
      });
    });

    setValidationErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    setIsFormValid(isValid);
    return isValid;
  }, [formNameTranslations, selectedCategory, elements]);

  // Run validation when form data changes, but only after form data is loaded
  useEffect(() => {
    if (formDataLoaded) {
      validateForm();
    }
  }, [validateForm, formDataLoaded]);

  const getDefaultOptions = (type: ElementType): ElementOption[] => {
    if (type === ElementType.RADIO_GROUP) {
      return [
        {
          id: generateId(),
          labelTranslations: { "EN": "Option 1", "TR": "Seçenek 1" },
          value: "option1",
          displayOrder: 0
        },
        {
          id: generateId(),
          labelTranslations: { "EN": "Option 2", "TR": "Seçenek 2" },
          value: "option2",
          displayOrder: 1
        },
        {
          id: generateId(),
          labelTranslations: { "EN": "Option 3", "TR": "Seçenek 3" },
          value: "option3",
          displayOrder: 2
        }
      ];
    }
    if (type === ElementType.SELECT || type === ElementType.MULTI_SELECT || type === ElementType.CHECKBOX_GROUP) {
      return [
        {
          id: generateId(),
          labelTranslations: { "EN": "Option 1", "TR": "Seçenek 1" },
          value: "option1",
          displayOrder: 0
        },
        {
          id: generateId(),
          labelTranslations: { "EN": "Option 2", "TR": "Seçenek 2" },
          value: "option2",
          displayOrder: 1
        }
      ];
    }
    return [];
  };

  const addElement = (type: ElementType) => {
    const newElement: FormElement = {
      id: generateId(),
      type,
      labelTranslations: {
        "EN": `New ${type.toLowerCase()} field`,
        "TR": `Yeni ${type.toLowerCase()} alanı`
      },
      descriptionTranslations: {},
      placeholderTranslations: {},
      helpTextTranslations: {},
      required: false,
      displayOrder: elements.length,
      options: needsOptions(type) ? getDefaultOptions(type) : undefined,
      validationRegex: undefined,
      minLength: undefined,
      maxLength: undefined,
      minValue: undefined,
      maxValue: undefined,
      properties: {},
    };

    setElements([...elements, newElement]);
    setSelectedElement(newElement);
  };

  const updateElement = (elementId: string, updates: Partial<FormElement>) => {
    setElements(elements.map(el =>
      el.id === elementId ? { ...el, ...updates } : el
    ));

    if (selectedElement?.id === elementId) {
      setSelectedElement({ ...selectedElement, ...updates });
    }
  };

  const deleteElement = (elementId: string) => {
    setElements(elements.filter(el => el.id !== elementId));
    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  };

  const moveElement = (elementId: string, direction: 'up' | 'down') => {
    const currentIndex = elements.findIndex(el => el.id === elementId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= elements.length) return;

    const newElements = [...elements];
    const [movedElement] = newElements.splice(currentIndex, 1);
    newElements.splice(newIndex, 0, movedElement);

    // Update display orders
    const updatedElements = newElements.map((el, index) => ({
      ...el,
      displayOrder: index
    }));

    setElements(updatedElements);
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const newElements = Array.from(elements);
    const [reorderedElement] = newElements.splice(result.source.index, 1);
    newElements.splice(result.destination.index, 0, reorderedElement);

    // Update display orders
    const updatedElements = newElements.map((el, index) => ({
      ...el,
      displayOrder: index
    }));

    setElements(updatedElements);
  };

  const handleReorder = (fromIndex: number, toIndex: number) => {
    const newElements = Array.from(elements);
    const [reorderedElement] = newElements.splice(fromIndex, 1);
    newElements.splice(toIndex, 0, reorderedElement);

    // Update display orders
    const updatedElements = newElements.map((el, index) => ({
      ...el,
      displayOrder: index
    }));

    setElements(updatedElements);
  };

  const addOption = (elementId: string) => {
    const element = elements.find(el => el.id === elementId);
    if (!element) return;

    const options = element.options || [];
    const newOption: ElementOption = {
      id: generateId(),
      labelTranslations: {
        "EN": 'New option',
        "TR": 'Yeni seçenek'
      },
      value: `option_${options.length + 1}`,
      displayOrder: options.length,
    };

    updateElement(elementId, {
      options: [...options, newOption]
    });
  };

  const updateOption = (elementId: string, optionId: string, updates: Partial<ElementOption>) => {
    const element = elements.find(el => el.id === elementId);
    if (!element || !element.options) return;

    const updatedOptions = element.options.map(opt =>
      opt.id === optionId ? { ...opt, ...updates } : opt
    );

    updateElement(elementId, { options: updatedOptions });
  };

  const deleteOption = (elementId: string, optionId: string) => {
    const element = elements.find(el => el.id === elementId);
    if (!element || !element.options) return;

    const updatedOptions = element.options.filter(opt => opt.id !== optionId);
    updateElement(elementId, { options: updatedOptions });
  };

  const saveForm = async () => {
    console.log('Save form clicked!');
    console.log('Form name translations:', formNameTranslations);
    console.log('Selected category:', selectedCategory);
    console.log('Elements:', elements);

    // Comprehensive frontend validation
    const validationErrors = [];

    // Validate form name in at least one language
    const hasFormName = Object.values(formNameTranslations).some(name => name?.trim());
    if (!hasFormName) {
      validationErrors.push('Form name is required in at least one language');
    }

    // Validate category selection (REQUIRED)
    if (!selectedCategory) {
      validationErrors.push('Category selection is required');
    } else if (categoryHasForm && !isEditMode) {
      validationErrors.push('Selected category already has an active form. Each category can only have one form template.');
    }

    // Validate elements
    if (elements.length === 0) {
      validationErrors.push('At least one form element is required');
    }

    // Validate each element
    elements.forEach((element, index) => {
      const hasLabel = Object.values(element.labelTranslations || {}).some(label => label?.trim());
      if (!hasLabel) {
        validationErrors.push(`Element ${index + 1}: Label is required in at least one language`);
      }

      // Validate options for elements that need them
      if (needsOptions(element.type) && (!element.options || element.options.length === 0)) {
        validationErrors.push(`Element ${index + 1}: At least one option is required for ${element.type}`);
      }
    });

    // Show validation errors if any
    if (validationErrors.length > 0) {
      console.log('Validation failed:', validationErrors);
      toast({
        title: 'Validation Error',
        description: validationErrors[0], // Show first error
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const formData = {
        categoryId: selectedCategory,
        nameTranslations: formNameTranslations,
        descriptionTranslations: formDescriptionTranslations,
        elements: elements.map(el => ({
          type: el.type,
          labelTranslations: el.labelTranslations,
          descriptionTranslations: el.descriptionTranslations || {},
          placeholderTranslations: el.placeholderTranslations || {},
          helpTextTranslations: el.helpTextTranslations || {},
          required: el.required,
          displayOrder: el.displayOrder,
          options: el.options?.map(opt => ({
            labelTranslations: opt.labelTranslations,
            value: opt.value,
            displayOrder: opt.displayOrder,
          })) || [],
          validationRegex: el.validationRegex,
          minLength: el.minLength,
          maxLength: el.maxLength,
          minValue: el.minValue,
          maxValue: el.maxValue,
          properties: el.properties || {},
        })),
        active: true,
      };

      console.log('Sending form data:', JSON.stringify(formData, null, 2));

      const url = isEditMode
        ? `/api/question-form-service/api/v1/form-templates/${editFormId}`
        : '/api/question-form-service/api/v1/form-templates';

      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: isEditMode ? 'Form template updated successfully!' : 'Form template created successfully!',
        });
        window.location.href = '/forms';
      } else {
        const errorText = await response.text();
        console.error('Server error:', errorText);
        throw new Error(`Failed to save form: ${response.status}`);
      }
    } catch (error) {
      console.error('Error saving form:', error);
      toast({
        title: 'Error',
        description: 'Failed to save form',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getCategoryName = (categoryId: number): string => {
    // Since we're using lazy loading, we can't get the category name here
    // This function is mainly used for display purposes
    return `Category ID: ${categoryId}`;
  };

  // Check if category already has a form
  const checkCategoryHasForm = async (categoryId: number) => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const response = await fetch(`${API_BASE_URL}/question-form-service/api/v1/form-templates/by-category/${categoryId}`);

      if (response.ok) {
        const forms = await response.json();
        const hasActiveForm = forms.some((form: any) => form.active);
        setCategoryHasForm(hasActiveForm);
        return hasActiveForm;
      }
      return false;
    } catch (error) {
      console.error('Error checking category forms:', error);
      return false;
    }
  };

  // Handle category selection with form check
  const handleCategoryChange = async (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    setCategoryHasForm(false);

    if (categoryId && !isEditMode) {
      await checkCategoryHasForm(categoryId);
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Loading Overlay for Edit Mode */}
        {formLoading && (
          <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900">Loading Form</h3>
              <p className="text-gray-600">Please wait while we load the form data...</p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => window.location.href = '/forms'}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Forms
            </Button>
            <div>
              <h1 className="text-3xl font-bold">
                {isEditMode ? 'Edit Form Template' : 'Form Builder'}
              </h1>
              <p className="text-muted-foreground">
                {isEditMode ? 'Update your form template' : 'Create dynamic forms with drag-and-drop interface'}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setShowPreview(true)}>
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </Button>
            <Button onClick={saveForm} disabled={loading || !isFormValid || !formDataLoaded}>
              <Save className="mr-2 h-4 w-4" />
              {loading ? 'Saving...' : !formDataLoaded ? 'Loading...' : (isEditMode ? 'Update Form' : 'Save Form')}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-12 gap-6">
          {/* Element Palette */}
          <div className="col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>Form Elements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {ELEMENT_TYPES.map(({ type, label, icon: Icon }) => (
                  <Button
                    key={type}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => addElement(type)}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {label}
                  </Button>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Form Canvas */}
          <div className="col-span-6">
            <Card>
              <CardHeader>
                <CardTitle>Form Design</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Form Settings */}
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label htmlFor="formNameJson">Form Name Translations * (JSON)</Label>
                    <Textarea
                      id="formNameJson"
                      value={JSON.stringify(formNameTranslations, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          setFormNameTranslations(parsed);
                        } catch (error) {
                          // Invalid JSON, don't update
                        }
                      }}
                      placeholder={`{
  "EN": "My Form",
  "TR": "Benim Formum",
  "DE": "Mein Formular"
}`}
                      rows={6}
                      className={validationErrors.formName ? 'border-red-500' : ''}
                      disabled={!formDataLoaded}
                    />
                    {validationErrors.formName ? (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.formName}</p>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1">
                        Enter form name translations as JSON object with language codes as keys
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="formDescJson">Form Description Translations (JSON)</Label>
                    <Textarea
                      id="formDescJson"
                      value={JSON.stringify(formDescriptionTranslations, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          setFormDescriptionTranslations(parsed);
                        } catch (error) {
                          // Invalid JSON, don't update
                        }
                      }}
                      placeholder={`{
  "EN": "Form description",
  "TR": "Form açıklaması",
  "DE": "Formularbeschreibung"
}`}
                      rows={6}
                      disabled={!formDataLoaded}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter form description translations as JSON object
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <CategorySelector
                      value={selectedCategory}
                      onChange={handleCategoryChange}
                      placeholder="Select a category..."
                      searchPlaceholder="Search categories..."
                      className={`w-full ${validationErrors.category ? 'border-red-500' : ''}`}
                      includeAllOption={false}
                    />
                    {validationErrors.category && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.category}</p>
                    )}
                    {categoryHasForm && !isEditMode && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-2">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-yellow-800">
                              Category Already Has a Form
                            </h3>
                            <div className="mt-2 text-sm text-yellow-700">
                              <p>This category already has an active form. Each category can only have one form template.</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Form Elements */}
                <div className="space-y-2 min-h-[200px]">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Form Elements</h3>
                    {validationErrors.elements && formDataLoaded && (
                      <p className="text-red-500 text-sm">{validationErrors.elements}</p>
                    )}
                  </div>

                  {!formDataLoaded && isEditMode ? (
                    <div className="text-center py-8 text-gray-500">
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                      </div>
                      <p className="mt-4">Loading form elements...</p>
                    </div>
                  ) : elements.length > 0 ? (
                    <div className="space-y-2">
                      {elements.map((element, index) => (
                        <div
                          key={element.id}
                          className={`p-4 border rounded-lg bg-white cursor-pointer hover:bg-gray-50 transition-colors ${
                            selectedElement?.id === element.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                          }`}
                          onClick={() => {
                            console.log('Element clicked:', element);
                            setSelectedElement(element);
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="text-gray-400" title="Use arrow buttons to reorder">
                                <GripVertical className="h-5 w-5" />
                              </div>
                              <div>
                                <div className="font-medium">
                                  {element.labelTranslations['EN'] || element.labelTranslations['TR'] || 'Untitled'}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {element.type} {element.required && '(Required)'}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  moveElement(element.id, 'up');
                                }}
                                disabled={index === 0}
                                title="Move up"
                              >
                                <ChevronUp className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  moveElement(element.id, 'down');
                                }}
                                disabled={index === elements.length - 1}
                                title="Move down"
                              >
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteElement(element.id);
                                }}
                                title="Delete"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                      <p className="text-lg font-medium">No elements added yet</p>
                      <p className="text-sm mt-1">Click elements from the palette to start building your form</p>
                      <p className="text-xs mt-2 text-gray-400">Use arrow buttons to reorder elements once added</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Properties Panel */}
          <div className="col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>Properties</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedElement ? (
                  <div>
                    <div className="mb-4 p-2 bg-blue-50 rounded">
                      <p className="text-sm text-blue-700">
                        Editing: {selectedElement.type} - {selectedElement.labelTranslations['EN'] || 'Untitled'}
                      </p>
                    </div>
                    <ElementPropertiesPanel
                      element={selectedElement}
                      needsOptions={needsOptions}
                      onUpdate={(updates) => {
                        updateElement(selectedElement.id, updates);
                      }}
                      onAddOption={() => addOption(selectedElement.id)}
                      onUpdateOption={(optionId, updates) => updateOption(selectedElement.id, optionId, updates)}
                      onDeleteOption={(optionId) => deleteOption(selectedElement.id, optionId)}
                    />
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <Settings className="mx-auto h-8 w-8 mb-2" />
                    <p>Select an element to edit its properties</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">Form Preview</h2>
              <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="text-lg font-medium">
                  {formNameTranslations.EN || formNameTranslations.TR || 'Untitled Form'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {formDescriptionTranslations.EN || formDescriptionTranslations.TR || 'No description provided'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Category: {selectedCategory ? getCategoryName(selectedCategory) : 'No category selected'}
                </p>
              </div>

              {elements.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No form elements added yet</p>
              ) : (
                <div className="space-y-4">
                  {elements
                    .sort((a, b) => a.displayOrder - b.displayOrder)
                    .map((element) => (
                      <PreviewFormElement key={element.id} element={element} />
                    ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}

interface ElementPropertiesPanelProps {
  element: FormElement;
  needsOptions: (type: ElementType) => boolean;
  onUpdate: (updates: Partial<FormElement>) => void;
  onAddOption: () => void;
  onUpdateOption: (optionId: string, updates: Partial<ElementOption>) => void;
  onDeleteOption: (optionId: string) => void;
}

function ElementPropertiesPanel({
  element,
  needsOptions,
  onUpdate,
  onAddOption,
  onUpdateOption,
  onDeleteOption,
}: ElementPropertiesPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="labelJson">Label Translations (JSON)</Label>
        <Textarea
          id="labelJson"
          value={JSON.stringify(element.labelTranslations, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onUpdate({ labelTranslations: parsed });
            } catch (error) {
              // Invalid JSON, don't update
            }
          }}
          placeholder={`{
  "EN": "Field Label",
  "TR": "Alan Etiketi"
}`}
          rows={4}
        />
      </div>

      <div>
        <Label htmlFor="descriptionJson">Description Translations (JSON)</Label>
        <Textarea
          id="descriptionJson"
          value={JSON.stringify(element.descriptionTranslations || {}, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onUpdate({ descriptionTranslations: parsed });
            } catch (error) {
              // Invalid JSON, don't update
            }
          }}
          placeholder={`{
  "EN": "Field description",
  "TR": "Alan açıklaması"
}`}
          rows={4}
        />
      </div>

      <div>
        <Label htmlFor="placeholderJson">Placeholder Translations (JSON)</Label>
        <Textarea
          id="placeholderJson"
          value={JSON.stringify(element.placeholderTranslations || {}, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onUpdate({ placeholderTranslations: parsed });
            } catch (error) {
              // Invalid JSON, don't update
            }
          }}
          placeholder={`{
  "EN": "Enter value...",
  "TR": "Değer girin..."
}`}
          rows={4}
        />
      </div>

      <div>
        <Label htmlFor="helpTextJson">Help Text Translations (JSON)</Label>
        <Textarea
          id="helpTextJson"
          value={JSON.stringify(element.helpTextTranslations || {}, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onUpdate({ helpTextTranslations: parsed });
            } catch (error) {
              // Invalid JSON, don't update
            }
          }}
          placeholder={`{
  "EN": "Help text here",
  "TR": "Yardım metni burada"
}`}
          rows={4}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="required"
          checked={element.required}
          onCheckedChange={(checked) => onUpdate({ required: checked })}
        />
        <Label htmlFor="required">Required</Label>
      </div>

      {needsOptions(element.type) && (
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Options</Label>
            {!element.properties?.referenceDataType && (
              <div className="flex space-x-2">
                <Button size="sm" onClick={onAddOption}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Reference Data Type Selector */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Reference Data Integration</Label>
            <p className="text-xs text-gray-600 mb-2">
              Select a reference data type to automatically populate options
            </p>
            <select
              value={element.properties?.referenceDataType || ''}
              onChange={async (e) => {
                const referenceDataType = e.target.value as ReferenceDataType;

                if (referenceDataType) {
                  try {
                    // Fetch reference data and create options
                    const referenceData = await referenceDataService.getActiveReferenceDataByType(referenceDataType);

                    const newOptions: ElementOption[] = referenceData.map((item, index) => {
                      // API'den gelen translations key'lerini büyük harfe çevir
                      const labelTranslations: Record<string, string> = {};
                      if (item.translations) {
                        Object.entries(item.translations).forEach(([key, value]) => {
                          labelTranslations[key.toUpperCase()] = value;
                        });
                      }

                      // Use English translation as value, fallback to Turkish, then code
                      const englishValue = item.translations?.EN || item.translations?.en;
                      const turkishValue = item.translations?.TR || item.translations?.tr;
                      const optionValue = englishValue || turkishValue || item.code;

                      return {
                        id: `ref_${item.id}`,
                        labelTranslations,
                        value: optionValue,
                        displayOrder: index,
                        defaultSelected: false
                      };
                    });

                    // Update element with reference data type and options
                    onUpdate({
                      properties: { ...element.properties, referenceDataType },
                      options: newOptions
                    });
                  } catch (error) {
                    console.error('Error loading reference data:', error);
                  }
                } else {
                  // Clear reference data type
                  const { referenceDataType: _, ...otherProperties } = element.properties || {};
                  onUpdate({
                    properties: otherProperties,
                    options: []
                  });
                }
              }}
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">Manual Options</option>
              {Object.values(ReferenceDataType).map((type) => (
                <option key={type} value={type}>
                  {type.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
            {element.properties?.referenceDataType && (
              <p className="text-xs text-green-600 mt-1">
                ✓ Options loaded from {element.properties.referenceDataType} reference data
              </p>
            )}
          </div>
          <div className="space-y-2">
            {element.options?.map((option) => (
              <div key={option.id} className="space-y-2 p-2 border rounded">
                <div className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Label className="text-xs">
                      Label Translations (JSON)
                      {String(option.id).startsWith('ref_') && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Reference Data
                        </Badge>
                      )}
                    </Label>
                    <Textarea
                      value={JSON.stringify(option.labelTranslations, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          onUpdateOption(option.id, { labelTranslations: parsed });
                        } catch (error) {
                          // Invalid JSON, don't update
                        }
                      }}
                      placeholder={`{
  "EN": "Option label",
  "TR": "Seçenek etiketi"
}`}
                      rows={3}
                      disabled={String(option.id).startsWith('ref_')} // Disable editing for reference data options
                    />
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteOption(option.id)}
                    disabled={String(option.id).startsWith('ref_')} // Disable deletion for reference data options
                    title={String(option.id).startsWith('ref_') ? 'Reference data options cannot be deleted manually' : 'Delete option'}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-xs text-gray-500">
                  Value: {option.value}
                  {String(option.id).startsWith('ref_') && (
                    <span className="ml-2 text-blue-600">(From Reference Data)</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {element.properties?.referenceDataType && (
            <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
              <strong>Note:</strong> Options are automatically loaded from {element.properties.referenceDataType} reference data.
              To add custom options, change to "Manual Options" above.
            </div>
          )}

          {!element.properties?.referenceDataType && element.options?.length === 0 && (
            <div className="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-600">
              No options added yet. Click the + button to add options manually or select a reference data type above.
            </div>
          )}
        </div>
      )}

      {/* Validation Rules */}
      <div className="space-y-2">
        <Label>Validation Rules</Label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="minLength" className="text-xs">Min Length</Label>
            <Input
              id="minLength"
              type="number"
              value={element.minLength || ''}
              onChange={(e) => onUpdate({
                minLength: e.target.value ? parseInt(e.target.value) : undefined
              })}
              placeholder="Min"
            />
          </div>
          <div>
            <Label htmlFor="maxLength" className="text-xs">Max Length</Label>
            <Input
              id="maxLength"
              type="number"
              value={element.maxLength || ''}
              onChange={(e) => onUpdate({
                maxLength: e.target.value ? parseInt(e.target.value) : undefined
              })}
              placeholder="Max"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="validationRegex" className="text-xs">Validation Regex</Label>
          <Input
            id="validationRegex"
            value={element.validationRegex || ''}
            onChange={(e) => onUpdate({
              validationRegex: e.target.value || undefined
            })}
            placeholder="^[a-zA-Z0-9]+$"
          />
        </div>
      </div>
    </div>
  );
}

// Preview Form Element Component
function PreviewFormElement({ element }: { element: FormElement }) {
  const getLabel = () => element.labelTranslations?.EN || element.labelTranslations?.TR || 'Untitled Field';
  const getPlaceholder = () => element.placeholderTranslations?.EN || element.placeholderTranslations?.TR || '';
  const getDescription = () => element.descriptionTranslations?.EN || element.descriptionTranslations?.TR || '';
  const getHelpText = () => element.helpTextTranslations?.EN || element.helpTextTranslations?.TR || '';

  // Helper function to get option label - handles both manual options and reference data
  const getOptionLabel = (option: any) => {
    // For reference data options (have translations field)
    if (option.translations) {
      return option.translations.EN || option.translations.TR || option.code || option.value;
    }
    // For manual options (have labelTranslations field)
    return option.labelTranslations?.EN || option.labelTranslations?.TR || option.value;
  };

  const renderElement = () => {
    switch (element.type) {
      case ElementType.TEXT:
        return (
          <input
            type="text"
            placeholder={getPlaceholder()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        );

      case ElementType.TEXTAREA:
        return (
          <textarea
            placeholder={getPlaceholder()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows={3}
          />
        );

      case ElementType.NUMBER:
        return (
          <input
            type="number"
            placeholder={getPlaceholder()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        );

      case ElementType.EMAIL:
        return (
          <input
            type="email"
            placeholder={getPlaceholder()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        );

      case ElementType.DATE:
        return (
          <input
            type="date"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        );

      case ElementType.CHECKBOX:
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" className="cursor-pointer" />
            <span className="cursor-pointer">{getLabel()}</span>
          </div>
        );

      case ElementType.RADIO_GROUP:
        return (
          <div className="space-y-2">
            {element.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={element.id}
                  value={option.value}
                  className="cursor-pointer"
                />
                <span className="cursor-pointer">{getOptionLabel(option)}</span>
              </div>
            ))}
          </div>
        );

      case ElementType.SELECT:
        return (
          <select className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer">
            <option value="">Select an option...</option>
            {element.options?.map((option) => (
              <option key={option.id} value={option.value}>
                {getOptionLabel(option)}
              </option>
            ))}
          </select>
        );

      case ElementType.CHECKBOX_GROUP:
        return (
          <div className="space-y-2">
            {element.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <input type="checkbox" className="cursor-pointer" />
                <span className="cursor-pointer">{getOptionLabel(option)}</span>
              </div>
            ))}
          </div>
        );

      case ElementType.MULTI_SELECT:
        return (
          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer"
            multiple
            size={Math.min(element.options?.length || 3, 5)}
          >
            {element.options?.map((option) => (
              <option key={option.id} value={option.value}>
                {getOptionLabel(option)}
              </option>
            ))}
          </select>
        );

      case ElementType.FILE:
        return (
          <input
            type="file"
            className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer"
          />
        );

      case ElementType.RATING:
        return (
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star key={star} className="h-5 w-5 text-gray-300 cursor-pointer hover:text-yellow-400" />
            ))}
          </div>
        );

      case ElementType.COLOR:
        return (
          <input
            type="color"
            className="w-16 h-10 border border-gray-300 rounded-md cursor-pointer"
          />
        );

      default:
        return <div className="text-gray-500">Unsupported element type</div>;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <label className="font-medium text-sm">
          {getLabel()}
          {element.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      </div>

      {getDescription() && (
        <p className="text-sm text-gray-600">{getDescription()}</p>
      )}

      {element.type !== ElementType.CHECKBOX && renderElement()}
      {element.type === ElementType.CHECKBOX && renderElement()}

      {getHelpText() && (
        <p className="text-xs text-gray-500">{getHelpText()}</p>
      )}
    </div>
  );
}
