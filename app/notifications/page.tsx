"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Calendar, CalendarIcon, Plus, Search, Filter, Bell, Mail, Smartphone, Eye, MousePointer } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import ModernLayout from "@/components/layout/ModernLayout";
import CreateNotificationForm from "@/components/notifications/CreateNotificationForm";

interface Notification {
  id: number;
  userId: number;
  eventId?: string;
  eventType?: string;
  notificationType: string;
  priority: string;
  titles: { [key: string]: string };
  messages: { [key: string]: string };
  showInBell: boolean;
  isRead: boolean;
  isClicked: boolean;
  readAt?: string;
  clickedAt?: string;
  sendEmail: boolean;
  sendPush: boolean;
  actionUrl?: string;
  iconUrl?: string;
  expiresAt?: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
  sourceService?: string;
  status: string;
  sentAt?: string;
  deliveryAttempts: number;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

interface NotificationStats {
  totalNotifications: number;
  recentCount: number;
  byType: { [key: string]: number };
  byStatus: { [key: string]: number };
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [showInBellFilter, setShowInBellFilter] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date>();
  const [endDate] = useState<Date>();
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);


  const notificationTypes = [
    "EMAIL", "SMS", "PUSH_NOTIFICATION", "IN_APP_NOTIFICATION",
    "INFO", "WARNING", "ERROR", "SUCCESS", "SYSTEM",
    "USER_ACTION", "FORM_SUBMISSION", "REQUEST_UPDATE",
    "BID_RECEIVED", "PAYMENT_COMPLETED", "MEMBERSHIP_UPGRADED"
  ];

  const priorities = ["LOW", "NORMAL", "HIGH", "URGENT"];
  const statuses = ["PENDING", "SENT", "DELIVERED", "FAILED", "CANCELLED"];

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: "20"
      });

      if (selectedType && selectedType !== "all") params.append("notificationType", selectedType);
      if (selectedStatus && selectedStatus !== "all") params.append("status", selectedStatus);
      if (showInBellFilter && showInBellFilter !== "all") params.append("showInBell", showInBellFilter);
      if (startDate) params.append("startDate", startDate.toISOString());
      if (endDate) params.append("endDate", endDate.toISOString());

      const response = await fetch(`http://localhost:8093/api/admin/notifications?${params}`);
      const data = await response.json();
      
      setNotifications(data.content || []);
      setTotalPages(data.totalPages || 0);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, selectedType, selectedStatus, showInBellFilter, startDate, endDate]);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch("http://localhost:8093/api/admin/notifications/statistics");
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  }, []);

  useEffect(() => {
    fetchNotifications();
    fetchStats();
  }, [fetchNotifications, fetchStats]);

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
      PENDING: "outline",
      SENT: "default",
      DELIVERED: "secondary",
      FAILED: "destructive",
      CANCELLED: "outline"
    };
    return <Badge variant={variants[status] || "default"}>{status}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
      LOW: "outline",
      NORMAL: "default",
      HIGH: "secondary",
      URGENT: "destructive"
    };
    return <Badge variant={variants[priority] || "default"}>{priority}</Badge>;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EMAIL": return <Mail className="h-4 w-4" />;
      case "SMS": return <Smartphone className="h-4 w-4" />;
      case "PUSH_NOTIFICATION": return <Bell className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const filteredNotifications = notifications.filter(notification =>
    notification.titles.EN?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.messages.EN?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.userId.toString().includes(searchTerm)
  );

  return (
    <ModernLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Notification Management</h1>
            <p className="text-muted-foreground">
              Manage user notifications and bell notifications
            </p>
          </div>
          <Button onClick={() => window.location.href = '/bulk-notifications'}>
            <Plus className="mr-2 h-4 w-4" />
            Manage Bulk Notifications
          </Button>
        </div>

        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalNotifications}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent (24h)</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.recentCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Sent</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.byStatus?.SENT || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats.byStatus?.FAILED || 0}</div>
              </CardContent>
            </Card>
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4 gap-x-6">
              <div>
                <Label htmlFor="search" className="mb-2 block">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="type" className="mb-2 block">Type</Label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    {notificationTypes.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status" className="mb-2 block">Status</Label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority" className="mb-2 block">Priority</Label>
                <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All priorities</SelectItem>
                    {priorities.map((priority) => (
                      <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="showInBell" className="mb-2 block">Bell Notification</Label>
                <Select value={showInBellFilter} onValueChange={setShowInBellFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="true">Show in Bell</SelectItem>
                    <SelectItem value="false">Don&apos;t Show in Bell</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Date Range</Label>
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "PPP") : "Start date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications Table */}
        <Card>
          <CardHeader>
            <CardTitle>Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">Loading...</div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Bell</TableHead>
                      <TableHead>Read</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredNotifications.map((notification) => (
                      <TableRow key={notification.id}>
                        <TableCell>{notification.id}</TableCell>
                        <TableCell>{notification.userId}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTypeIcon(notification.notificationType)}
                            {notification.notificationType}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {notification.titles.EN || notification.titles.TR || notification.titles.DE}
                        </TableCell>
                        <TableCell>{getPriorityBadge(notification.priority)}</TableCell>
                        <TableCell>{getStatusBadge(notification.status)}</TableCell>
                        <TableCell>
                          {notification.showInBell ? (
                            <Bell className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Bell className="h-4 w-4 text-gray-300" />
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {notification.isRead && <Eye className="h-4 w-4 text-green-600" />}
                            {notification.isClicked && <MousePointer className="h-4 w-4 text-blue-600" />}
                          </div>
                        </TableCell>
                        <TableCell>
                          {format(new Date(notification.createdAt), "MMM dd, yyyy HH:mm")}
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage + 1} of {totalPages}
                  </div>
                  <div className="space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                      disabled={currentPage === 0}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                      disabled={currentPage >= totalPages - 1}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
