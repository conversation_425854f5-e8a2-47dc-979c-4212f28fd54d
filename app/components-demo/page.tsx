'use client';

import { useState } from 'react';
import {
  EncryptButton,
  LoadAndErrorButton,
  ShinyButton,
  SpotlightButton,
  DrawOutlineButton,
  MarqueeButton,
  WetPaintButton,
  ChipTabs,
  SlideTabs,
  DarkModeToggle,
  SliderToggle,
  BeamInput,
  SpinLoader,
  PulseLoader,
  ToastContainer,
  ToastType
} from '@/components';

export default function ComponentsDemo() {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    type: ToastType;
    title: string;
    message?: string;
  }>>([]);

  const [darkMode, setDarkMode] = useState<'light' | 'dark'>('light');
  const [toggleValue, setToggleValue] = useState<'light' | 'dark'>('light');

  const addToast = (type: ToastType, title: string, message?: string) => {
    const newToast = {
      id: Date.now().toString(),
      type,
      title,
      message
    };
    setToasts(prev => [...prev, newToast]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  };

  const tabs = ['Home', 'About', 'Services', 'Contact'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">
          Components Demo
        </h1>

        {/* Buttons Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Buttons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Encrypt Button</h3>
              <EncryptButton 
                text="Secure Login"
                onClick={() => addToast('success', 'Encrypted!', 'Data secured successfully')}
              />
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Load & Error Button</h3>
              <LoadAndErrorButton
                onClick={async () => {
                  await new Promise(resolve => setTimeout(resolve, 2000));
                  if (Math.random() > 0.5) throw new Error('Random error');
                }}
                loadingText="Processing..."
                successText="Success!"
                errorText="Failed!"
              >
                Try Me
              </LoadAndErrorButton>
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Shiny Button</h3>
              <ShinyButton onClick={() => addToast('info', 'Shiny!', 'Button clicked')}>
                Hover Me
              </ShinyButton>
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Spotlight Button</h3>
              <SpotlightButton onClick={() => addToast('info', 'Spotlight!', 'Button clicked')}>
                Follow Mouse
              </SpotlightButton>
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Draw Outline Button</h3>
              <DrawOutlineButton 
                variant="indigo"
                onClick={() => addToast('info', 'Outlined!', 'Button clicked')}
              >
                Draw Outline
              </DrawOutlineButton>
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Marquee Button</h3>
              <MarqueeButton 
                variant="blue"
                onClick={() => addToast('info', 'Marquee!', 'Button clicked')}
              >
                Scroll Text
              </MarqueeButton>
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Wet Paint Button</h3>
              <WetPaintButton 
                variant="violet"
                onClick={() => addToast('info', 'Wet Paint!', 'Button clicked')}
              >
                Drip Effect
              </WetPaintButton>
            </div>
          </div>
        </section>

        {/* Tabs Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Tabs</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Chip Tabs</h3>
              <ChipTabs 
                tabs={tabs}
                onTabChange={(tab) => addToast('info', 'Tab Changed', `Selected: ${tab}`)}
                variant="violet"
              />
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Slide Tabs</h3>
              <SlideTabs 
                tabs={tabs}
                onTabChange={(tab) => addToast('info', 'Tab Changed', `Selected: ${tab}`)}
                variant="blue"
              />
            </div>
          </div>
        </section>

        {/* Toggles Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Toggles</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Dark Mode Toggle</h3>
              <DarkModeToggle 
                mode={darkMode}
                setMode={setDarkMode}
                onChange={(mode) => addToast('info', 'Mode Changed', `Switched to ${mode} mode`)}
              />
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Slider Toggle</h3>
              <SliderToggle 
                selected={toggleValue}
                setSelected={setToggleValue}
                onChange={(value) => addToast('info', 'Toggle Changed', `Selected: ${value}`)}
                variant="green"
              />
            </div>
          </div>
        </section>

        {/* Inputs Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Inputs</h2>
          <div className="bg-slate-800 p-6 rounded-lg">
            <h3 className="text-white mb-4">Beam Input</h3>
            <BeamInput 
              placeholder="Enter your email"
              buttonText="Subscribe"
              onSubmit={(value) => addToast('success', 'Subscribed!', `Email: ${value}`)}
              variant="purple"
            />
          </div>
        </section>

        {/* Loaders Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Loaders</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Spin Loader</h3>
              <SpinLoader size="lg" color="text-violet-500" />
            </div>

            <div className="bg-slate-800 p-6 rounded-lg">
              <h3 className="text-white mb-4">Pulse Loader</h3>
              <PulseLoader size="lg" color="bg-blue-500" />
            </div>
          </div>
        </section>

        {/* Toast Actions */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Toast Notifications</h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => addToast('success', 'Success!', 'Operation completed')}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
            >
              Success Toast
            </button>
            <button
              onClick={() => addToast('error', 'Error!', 'Something went wrong')}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
            >
              Error Toast
            </button>
            <button
              onClick={() => addToast('warning', 'Warning!', 'Please be careful')}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded"
            >
              Warning Toast
            </button>
            <button
              onClick={() => addToast('info', 'Info!', 'Here is some information')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
            >
              Info Toast
            </button>
          </div>
        </section>
      </div>

      {/* Toast Container */}
      <ToastContainer 
        toasts={toasts}
        onClose={removeToast}
        position="top-right"
      />
    </div>
  );
}
