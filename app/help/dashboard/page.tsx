'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, BarChart3, Users, FileText, Activity, AlertTriangle, TrendingUp } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { useRouter } from 'next/navigation';

export default function DashboardHelpPage() {
  const router = useRouter();

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/help')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Help Home
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Help</h1>
            <p className="text-gray-600">Main control panel usage guide</p>
          </div>
        </div>

        {/* Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Dashboard Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              Dashboard is the main control center of the LookForX system. You can access all important
              information from one place with system status, statistics and quick access menus.
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Main Features:</h4>
              <ul className="list-disc list-inside text-blue-800 space-y-1">
                <li>Real-time system statistics</li>
                <li>Quick access menus (Quick Actions)</li>
                <li>Recent activities and notifications</li>
                <li>System status indicators</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        <Card>
          <CardHeader>
            <CardTitle>Statistics Cards</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold">User Statistics</h4>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Users className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Total Users</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Shows the total number of users registered in the system.
                    Active/inactive user distinction is made.
                  </p>
                  <div className="mt-2 text-xs text-green-700">
                    <strong>Endpoint:</strong> GET /api/users/statistics
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">Form Statistics</h4>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="h-5 w-5 text-orange-600" />
                    <span className="font-medium">Form Submissions</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Daily, weekly and monthly form submission statistics.
                    Displayed with trend analysis.
                  </p>
                  <div className="mt-2 text-xs text-orange-700">
                    <strong>Endpoint:</strong> GET /api/forms/submissions/statistics
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Access Menus (Quick Actions)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-700">
                Quick Actions provide fast access to the most frequently used operations.
                Each card redirects to the relevant module and shows instant information.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">User Management</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    User list, adding new users and profile editing operations.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Redirect:</strong> /users
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Category Management</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Category management with hierarchical category structure and multi-language support.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Redirect:</strong> /categories
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Form Builder</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Dynamic form creation and form submission management.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Redirect:</strong> /forms
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Event Monitoring</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    System events, monitoring and replay operations.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Redirect:</strong> /events
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle>Dashboard API Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Main Statistics</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/dashboard/statistics</span>
                    <span className="text-gray-600">General system statistics</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/users/count</span>
                    <span className="text-gray-600">Total user count</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/forms/submissions/count</span>
                    <span className="text-gray-600">Total form submissions</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/categories/count</span>
                    <span className="text-gray-600">Total category count</span>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">System Status</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/health</span>
                    <span className="text-gray-600">System health status</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/events/statistics</span>
                    <span className="text-gray-600">Event statistics</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-mono">GET /api/exceptions/recent</span>
                    <span className="text-gray-600">Recent system errors</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Regular Monitoring</h4>
                  <p className="text-sm text-gray-600">
                    Monitor the dashboard daily to track system status.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Quick Access</h4>
                  <p className="text-sm text-gray-600">
                    Use Quick Actions cards to provide fast access to frequently used operations.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Trend Tracking</h4>
                  <p className="text-sm text-gray-600">
                    Monitor system performance by tracking trend indicators in statistics cards.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
