'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Users, UserPlus, Edit, Trash2, Search, Filter, Shield } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { useRouter } from 'next/navigation';

export default function UsersHelpPage() {
  const router = useRouter();

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/help')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Help Home
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">User Management Help</h1>
            <p className="text-gray-600">User management module usage guide</p>
          </div>
        </div>

        {/* Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              User Management Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              With the user management module, you can view, edit and manage all users in the system.
              You can perform role-based authorization and profile management operations through this module.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Main Features:</h4>
                <ul className="list-disc list-inside text-blue-800 space-y-1 text-sm">
                  <li>User list and search</li>
                  <li>Profile information editing</li>
                  <li>Role and permission management</li>
                  <li>User status control</li>
                </ul>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Backend Service:</h4>
                <ul className="list-disc list-inside text-green-800 space-y-1 text-sm">
                  <li>user-service (Port: 8081)</li>
                  <li>PostgreSQL database</li>
                  <li>Redis cache support</li>
                  <li>JWT authentication</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <Card>
          <CardHeader>
            <CardTitle>Features and Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* User List */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center">
                  <Search className="h-4 w-4 mr-2" />
                  User List and Search
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  You can view all users with pagination and search by name, email or role.
                </p>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <strong>Usage:</strong> Filter by typing username, email or role in the search box.
                  You can adjust the number of users to be displayed per page.
                </div>
              </div>

              {/* User Profile */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center">
                  <Edit className="h-4 w-4 mr-2" />
                  Profile Editing
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  You can edit user profile information and change passwords.
                </p>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <strong>Editable Fields:</strong> First Name, Last Name, Email, Phone, Address, Profile Photo, Status
                </div>
              </div>

              {/* Role Management */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Role and Permission Management
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  You can assign roles to users and edit permissions.
                </p>
                <div className="flex space-x-2 mb-2">
                  <Badge variant="default">ADMIN</Badge>
                  <Badge variant="secondary">USER</Badge>
                  <Badge variant="outline">MODERATOR</Badge>
                </div>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <strong>Roles:</strong> ADMIN (Full access), USER (Basic user), MODERATOR (Mid-level access)
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle>API Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 text-blue-900">User Operations</h4>
                <div className="space-y-2 text-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/users</span>
                      <p className="text-gray-600 mt-1">User list (pagination)</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/users/{id}</span>
                      <p className="text-gray-600 mt-1">User details</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/users</span>
                      <p className="text-gray-600 mt-1">Create new user</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">PUT /api/users/{id}</span>
                      <p className="text-gray-600 mt-1">Update user</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">DELETE /api/users/{id}</span>
                      <p className="text-gray-600 mt-1">Delete user</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/users/search</span>
                      <p className="text-gray-600 mt-1">Search users</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 text-green-900">Role and Permission Operations</h4>
                <div className="space-y-2 text-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/users/{id}/roles</span>
                      <p className="text-gray-600 mt-1">User roles</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/users/{id}/roles</span>
                      <p className="text-gray-600 mt-1">Assign role</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">DELETE /api/users/{id}/roles/{roleId}</span>
                      <p className="text-gray-600 mt-1">Remove role</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/roles</span>
                      <p className="text-gray-600 mt-1">All roles</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Structure */}
        <Card>
          <CardHeader>
            <CardTitle>Data Structure</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-3">User Entity</h4>
              <pre className="text-sm overflow-x-auto">
{`{
  "id": 1,
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+90 ************",
  "address": "İstanbul, Türkiye",
  "profileImageUrl": "https://...",
  "isActive": true,
  "roles": ["USER", "MODERATOR"],
  "createdAt": "2025-01-01T10:00:00",
  "updatedAt": "2025-01-01T10:00:00"
}`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle>Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Secure Password Policy</h4>
                  <p className="text-sm text-gray-600">
                    Set strong password requirements for users and recommend regular password changes.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Role-Based Access</h4>
                  <p className="text-sm text-gray-600">
                    Give users only the necessary permissions and regularly review role assignments.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Data Validation</h4>
                  <p className="text-sm text-gray-600">
                    Verify the accuracy of email addresses and phone numbers.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
