'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  FolderTree, 
  FileText, 
  Activity, 
  Database, 
  AlertTriangle, 
  BarChart3, 
  Settings,
  BookOpen,
  ExternalLink,
  ArrowRight
} from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { useRouter } from 'next/navigation';

interface HelpSection {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}

export default function HelpPage() {
  const router = useRouter();

  const helpSections: HelpSection[] = [
    {
      title: 'Dashboard',
      description: 'Main control panel, system overview and quick access menus',
      icon: <BarChart3 className="h-6 w-6" />, 
      href: '/dashboard',
      color: 'bg-blue-100 text-blue-600'
    },
    {
      title: 'User Management',
      description: 'User management, profile editing and role assignment operations',
      icon: <Users className="h-6 w-6" />, 
      href: '/users',
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Category Management',
      description: 'Hierarchical category structure and multi-language support',
      icon: <FolderTree className="h-6 w-6" />, 
      href: '/categories',
      color: 'bg-purple-100 text-purple-600'
    },
    {
      title: 'Form Builder',
      description: 'Dynamic form creation and form submission management',
      icon: <FileText className="h-6 w-6" />, 
      href: '/forms',
      color: 'bg-orange-100 text-orange-600'
    },
    {
      title: 'Event Management',
      description: 'Event sourcing, monitoring and replay operations',
      icon: <Activity className="h-6 w-6" />, 
      href: '/events',
      color: 'bg-yellow-100 text-yellow-600'
    },
    {
      title: 'Reference Data',
      description: 'System reference data and multi-language support',
      icon: <Database className="h-6 w-6" />, 
      href: '/reference-data',
      color: 'bg-indigo-100 text-indigo-600'
    },
    {
      title: 'Exception Handling',
      description: 'System errors and exception monitoring',
      icon: <AlertTriangle className="h-6 w-6" />, 
      href: '/exceptions',
      color: 'bg-red-100 text-red-600'
    },
    {
      title: 'System Settings',
      description: 'System configuration and general settings',
      icon: <Settings className="h-6 w-6" />, 
      href: '/settings',
      color: 'bg-gray-100 text-gray-600'
    }
  ];

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2">Admin Panel Help</h1>
          <p className="text-lg text-black dark:text-white max-w-2xl mx-auto">
            Learn all features of LookForX Admin Panel. Detailed explanations for each module,
            usage guides and API endpoint information.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">8</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Main Modules</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">25+</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">API Endpoints</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">6</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Microservices</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">3</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Language Support</div>
            </CardContent>
          </Card>
        </div>

        {/* Help Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {helpSections.map((section, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${section.color}`}>
                    {section.icon}
                  </div>
                  <CardTitle className="text-lg text-black dark:text-white">{section.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-black dark:text-white mb-4">{section.description}</p>
                <Button 
                  variant="outline" 
                  className="w-full cursor-pointer"
                  onClick={() => router.push(section.href)}
                >
                  View Details
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="outline" onClick={() => router.push('/help/api-reference')}>
                <ExternalLink className="h-4 w-4 mr-2" />
                API Reference
              </Button>
              <Button variant="outline" onClick={() => router.push('/help/troubleshooting')}>
                <AlertTriangle className="h-4 w-4 mr-2" />
                Troubleshooting
              </Button>
              <Button variant="outline" onClick={() => router.push('/help/best-practices')}>
                <BookOpen className="h-4 w-4 mr-2" />
                Best Practices
              </Button>
              <Button variant="outline" onClick={() => router.push('/help/changelog')}>
                <Activity className="h-4 w-4 mr-2" />
                Changelog
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Architecture */}
        <Card>
          <CardHeader>
            <CardTitle>System Architecture</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-900">Frontend</h3>
                  <p className="text-sm text-blue-700">Next.js + React + Tailwind CSS</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <h3 className="font-semibold text-green-900">Backend</h3>
                  <p className="text-sm text-green-700">Spring Boot Microservices</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <h3 className="font-semibold text-purple-900">Database</h3>
                  <p className="text-sm text-purple-700">PostgreSQL + MongoDB + Redis</p>
                </div>
              </div>
              <div className="text-center">
                <Button variant="outline" onClick={() => router.push('/help/architecture')}>
                  Detailed Architecture Info
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
