'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Eye, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useErrorNotification } from '@/contexts/ErrorContext';
import categoryService from '@/lib/category-service';
import { Category, CategoryType, SearchMode } from '@/lib/types/category';
import CategoryForm from '@/components/categories/CategoryForm';
import CategorySearch from '@/components/categories/CategorySearch';
import ModernLayout from '@/components/layout/ModernLayout';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [rootCategories, setRootCategories] = useState<Category[]>([]);
  const [selectedRootCategory, setSelectedRootCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [selectedType, setSelectedType] = useState<CategoryType>(CategoryType.PRODUCT);
  const [isMounted, setIsMounted] = useState(false);
  const { toast } = useToast();
  const { handleError, showSuccess } = useErrorNotification();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    loadRootCategories();
  }, [selectedType]);

  useEffect(() => {
    if (selectedRootCategory) {
      loadCategoriesForRoot(selectedRootCategory.id);
    }
  }, [selectedRootCategory]);

  const loadRootCategories = async () => {
    try {
      setLoading(true);
      console.log('Loading root categories for type:', selectedType);
      const data = await categoryService.getRootCategoriesByType(selectedType);
      console.log('Root categories loaded:', data);
      setRootCategories(data);

      // Auto-select first root category if available
      if (data.length > 0 && !selectedRootCategory) {
        setSelectedRootCategory(data[0]);
      } else if (data.length === 0) {
        setSelectedRootCategory(null);
        setCategories([]);
      }
    } catch (error) {
      handleError(error, {
        customMessage: 'Failed to load root categories',
        showRetry: true,
        onRetry: loadRootCategories
      });
    } finally {
      setLoading(false);
    }
  };

  const loadCategoriesForRoot = async (rootCategoryId: number) => {
    try {
      setLoading(true);
      // Get all categories and filter for the selected root category's children
      const allCategories = await categoryService.getAllCategories();

      // Find the selected root category and its subcategories
      const rootCategory = allCategories.find(cat => cat.id === rootCategoryId);
      if (rootCategory && rootCategory.subcategories) {
        setCategories(rootCategory.subcategories);

        // Auto-expand all categories by default
        const allCategoryIds = new Set<number>();
        const collectIds = (cats: Category[]) => {
          cats.forEach(cat => {
            allCategoryIds.add(cat.id);
            if (cat.subcategories && cat.subcategories.length > 0) {
              collectIds(cat.subcategories);
            }
          });
        };
        collectIds(rootCategory.subcategories);
        setExpandedCategories(allCategoryIds);
      } else {
        setCategories([]);
        setExpandedCategories(new Set());
      }
    } catch (error) {
      handleError(error, {
        customMessage: 'Failed to load categories',
        showRetry: true,
        onRetry: () => loadCategoriesForRoot(rootCategoryId)
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (query: string, mode: SearchMode, language?: string) => {
    if (!query.trim()) {
      setShowSearchResults(false);
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      console.log('Searching categories:', { query, mode, language });

      const results = await categoryService.searchCategories({
        query: query.trim(),
        mode,
        language,
        limit: 50
      });

      console.log('Search results:', results);
      setSearchResults(results);
      setShowSearchResults(true);

      toast({
        title: 'Search completed',
        description: `Found ${results.length} categories`,
      });
    } catch (error) {
      console.error('Error searching categories:', error);
      toast({
        title: 'Search failed',
        description: 'Failed to search categories',
        variant: 'destructive',
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleClearSearch = () => {
    setSearchResults([]);
    setShowSearchResults(false);
    setIsSearching(false);
  };

  const handleDeleteCategory = async (id: number) => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      await categoryService.deleteCategory(id);
      showSuccess("Category deleted successfully");

      // Refresh the categories list
      await loadRootCategories();

      // Also refresh subcategories if a root category is selected
      if (selectedRootCategory) {
        await loadCategoriesForRoot(selectedRootCategory.id);
      }
    } catch (error: any) {
      let customMessage = "Failed to delete category";
      if (error.response?.data?.message) {
        const backendMessage = error.response.data.message;
        if (backendMessage.includes('subcategories')) {
          customMessage = "Cannot delete category that has subcategories. Please delete subcategories first.";
        } else if (backendMessage.includes('not found')) {
          customMessage = "Category not found";
        } else {
          customMessage = backendMessage;
        }
      }

      handleError(error, {
        customMessage,
        showRetry: true,
        onRetry: () => handleDeleteCategory(id)
      });
    }
  };

  const handleClearCache = async () => {
    if (!confirm('Are you sure you want to clear all category cache? This will temporarily slow down category loading.')) {
      return;
    }

    try {
      await categoryService.clearCache();
      toast({
        title: "Success",
        description: "Category cache cleared successfully",
      });
    } catch (error: any) {
      console.error('Clear cache error:', error);
      toast({
        title: "Clear Cache Error",
        description: "Failed to clear category cache",
        variant: "destructive",
      });
    }
  };

  const handleFormSubmit = async () => {
    try {
      // Close the modal
      setShowForm(false);
      setEditingCategory(null);

      // Show success message
      showSuccess(editingCategory ? "Category updated successfully" : "Category created successfully");

      // Refresh the categories list
      await loadRootCategories();

      // Also refresh subcategories if a root category is selected
      if (selectedRootCategory) {
        await loadCategoriesForRoot(selectedRootCategory.id);
      }

      // Clear search results if any
      handleClearSearch();
    } catch (error) {
      console.error('Error after form submit:', error);
    }
  };

  if (loading /* || isSearching || any other loading state */) {
    return <LoadingSpinner />;
  }

  return (
    <ModernLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">Category Management</h1>
            <p className="mt-2 text-black dark:text-white">
              Organize and manage your product and service categories
            </p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={() => setShowForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
            <Button
              variant="outline"
              onClick={handleClearCache}
              className="text-orange-600 border-orange-600 hover:bg-orange-50"
            >
              Clear Cache
            </Button>
          </div>
        </div>

        {/* Category Type Filter */}
        <div>
          <div className="flex space-x-2">
            <Button
              variant={selectedType === CategoryType.PRODUCT ? 'default' : 'outline'}
              onClick={() => setSelectedType(CategoryType.PRODUCT)}
            >
              Products
            </Button>
            <Button
              variant={selectedType === CategoryType.SERVICE ? 'default' : 'outline'}
              onClick={() => setSelectedType(CategoryType.SERVICE)}
            >
              Services
            </Button>
          </div>
        </div>

        {/* Search Component */}
        <CategorySearch onSearch={handleSearch} onClear={handleClearSearch} />

        {/* Search Results */}
        {showSearchResults && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>
                Search Results
                <span className="text-sm font-normal text-gray-500 ml-2">
                  ({isSearching ? 'Searching...' : `${searchResults.length} results`})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isSearching ? (
                <LoadingSpinner />
              ) : searchResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No categories found for your search.
                </div>
              ) : (
                <div className="space-y-2">
                  {searchResults.map((result) => (
                    <Card key={result.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">
                              {result.translations?.EN || result.translations?.en || 'Unknown'}
                            </span>
                            <Badge variant={result.type === 'PRODUCT' ? 'default' : 'secondary'}>
                              {result.type}
                            </Badge>
                            <Badge variant="outline">Level {result.level}</Badge>
                            <Badge variant="outline">ID: {result.id}</Badge>
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {result.translations?.TR || result.translations?.tr || ''}
                          </div>
                          {result.parentId && (
                            <div className="text-xs text-gray-400 mt-1">
                              Parent ID: {result.parentId}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Convert search result to Category format for editing
                              const categoryForEdit: Category = {
                                id: parseInt(result.id),
                                parentId: result.parentId,
                                translations: result.translations,
                                type: result.type as CategoryType,
                                level: result.level,
                                createdAt: result.createdAt,
                                updatedAt: result.updatedAt,
                                createdBy: result.createdBy,
                                subcategories: []
                              };
                              setEditingCategory(categoryForEdit);
                              setShowForm(true);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCategory(parseInt(result.id))}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Categories Display */}
        <Card>
          <CardHeader>
            <CardTitle>
              {selectedType === CategoryType.PRODUCT ? 'Product' : 'Service'} Categories
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({rootCategories.length} main categories)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <LoadingSpinner />
            ) : rootCategories.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No main categories found. Create your first main category!
              </div>
            ) : (
              <div className="space-y-4">
                {/* Root Categories List */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {rootCategories.map((rootCat) => (
                    <Card key={rootCat.id} className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => setSelectedRootCategory(rootCat)}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">
                              {rootCat.translations.EN || rootCat.translations.en || 'Unknown'}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {rootCat.translations.TR || rootCat.translations.tr || ''}
                            </p>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant={rootCat.type === CategoryType.PRODUCT ? 'default' : 'secondary'}>
                                {rootCat.type}
                              </Badge>
                              <Badge variant="outline">ID: {rootCat.id}</Badge>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingCategory(rootCat);
                                setShowForm(true);
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteCategory(rootCat.id);
                              }}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Selected Root Category Details */}
                {selectedRootCategory && (
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle className="text-lg">
                        Subcategories of: {selectedRootCategory.translations.EN || selectedRootCategory.translations.en}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {categories.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          No subcategories found for this category.
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {categories.map((category) => (
                            <CategoryTreeItem
                              key={category.id}
                              category={category}
                              expandedCategories={expandedCategories}
                              onToggleExpand={(id) => {
                                const newExpanded = new Set(expandedCategories);
                                if (newExpanded.has(id)) {
                                  newExpanded.delete(id);
                                } else {
                                  newExpanded.add(id);
                                }
                                setExpandedCategories(newExpanded);
                              }}
                              onEdit={(cat) => {
                                setEditingCategory(cat);
                                setShowForm(true);
                              }}
                              onDelete={handleDeleteCategory}
                            />
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Category Form Modal */}
        {showForm && (
          <CategoryForm
            category={editingCategory}
            onSubmit={handleFormSubmit}
            onCancel={() => {
              setShowForm(false);
              setEditingCategory(null);
            }}
          />
        )}
      </div>
    </ModernLayout>
  );
}

// CategoryTreeItem Component
interface CategoryTreeItemProps {
  category: Category;
  expandedCategories: Set<number>;
  onToggleExpand: (id: number) => void;
  onEdit: (category: Category) => void;
  onDelete: (id: number) => void;
  level?: number;
}

const CategoryTreeItem: React.FC<CategoryTreeItemProps> = ({
  category,
  expandedCategories,
  onToggleExpand,
  onEdit,
  onDelete,
  level = 0
}) => {
  const isExpanded = expandedCategories.has(category.id);
  const hasSubcategories = category.subcategories && category.subcategories.length > 0;

  return (
    <div className={`border rounded-lg p-3 ${level > 0 ? 'ml-6 border-l-2 border-blue-200' : ''}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {hasSubcategories && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleExpand(category.id)}
              className="p-1 h-6 w-6"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          {!hasSubcategories && <div className="w-6" />}

          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="font-medium">
                {category.translations.EN || category.translations.en || 'Unknown'}
              </span>
              <Badge variant={category.type === CategoryType.PRODUCT ? 'default' : 'secondary'}>
                {category.type}
              </Badge>
              <Badge variant="outline">Level {category.level}</Badge>
            </div>
            <span className="text-sm text-gray-500">
              {category.translations.TR || category.translations.tr || ''}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(category)}
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(category.id)}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Subcategories */}
      {hasSubcategories && isExpanded && (
        <div className="mt-3 space-y-2">
          {category.subcategories!.map((subcategory) => (
            <CategoryTreeItem
              key={subcategory.id}
              category={subcategory}
              expandedCategories={expandedCategories}
              onToggleExpand={onToggleExpand}
              onEdit={onEdit}
              onDelete={onDelete}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};
