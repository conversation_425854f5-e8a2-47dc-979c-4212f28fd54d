'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Search, RefreshCw, Play, Filter } from 'lucide-react';
import { format } from 'date-fns';
import ModernLayout from '@/components/layout/ModernLayout';
import { useToast } from '@/contexts/ToastContext';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

interface EventStore {
  id: string;
  eventId: string;
  eventType: string;
  serviceName: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  timestamp: string;
  userId?: number;
  status: 'RECEIVED' | 'PROCESSING' | 'PROCESSED' | 'FAILED' | 'REPLAYED';
  topic: string;
  partition: number;
  offset: number;
  errorMessage?: string;
  retryCount: number;
  isReplayed: boolean;
  replayedAt?: string;
}

interface EventStatistics {
  totalEvents: number;
  processedEvents: number;
  failedEvents: number;
  replayedEvents: number;
  startDate: string;
  endDate: string;
}

export default function EventsPage() {
  const [events, setEvents] = useState<EventStore[]>([]);
  const [statistics, setStatistics] = useState<EventStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  
  // Filters
  const [serviceName, setServiceName] = useState('');
  const [eventType, setEventType] = useState('');
  const [status, setStatus] = useState('');
  const [userId, setUserId] = useState('');
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  
  const toast = useToast();

  const services = [
    'user-service', 'category-service', 'form-service', 'request-service',
    'bid-service', 'payment-service', 'campaign-service', 'notification-service'
  ];

  const eventTypes = [
    'USER_REGISTERED', 'USER_LOGIN', 'FORM_SUBMITTED', 'REQUEST_CREATED',
    'REQUEST_STATUS_CHANGED', 'BID_PLACED', 'PAYMENT_COMPLETED',
    'CAMPAIGN_CREATED', 'MEMBERSHIP_UPGRADED'
  ];

  const statuses = ['RECEIVED', 'PROCESSING', 'PROCESSED', 'FAILED', 'REPLAYED'];

  useEffect(() => {
    fetchEvents();
    fetchStatistics();
  }, [page, serviceName, eventType, status, userId, startDate, endDate]);

  const fetchEvents = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        size: '20'
      });

      if (serviceName) params.append('serviceName', serviceName);
      if (eventType) params.append('eventType', eventType);
      if (status) params.append('status', status);
      if (userId) params.append('userId', userId);
      if (startDate) params.append('startDate', startDate.toISOString());
      if (endDate) params.append('endDate', endDate.toISOString());

      const response = await fetch(`http://localhost:8086/api/events?${params}`);
      if (response.ok) {
        const data = await response.json();
        setEvents(data.content);
        setTotalPages(data.totalPages);
      } else {
        throw new Error('Failed to fetch events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      toast.error('Failed to fetch events');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate.toISOString());
      if (endDate) params.append('endDate', endDate.toISOString());

      const response = await fetch(`http://localhost:8086/api/events/statistics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const clearFilters = () => {
    setServiceName('');
    setEventType('');
    setStatus('');
    setUserId('');
    setStartDate(undefined);
    setEndDate(undefined);
    setPage(0);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      RECEIVED: 'default',
      PROCESSING: 'secondary',
      PROCESSED: 'success',
      FAILED: 'destructive',
      REPLAYED: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status}
      </Badge>
    );
  };

  if (loading) return <LoadingSpinner />;

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">Event Store</h1>
            <p className="text-black dark:text-white">Monitor and manage system events</p>
          </div>
          <Button onClick={() => window.location.href = '/events/replay'}>
            <Play className="h-4 w-4 mr-2" />
            Event Replay
          </Button>
        </div>

        {/* Statistics */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">{statistics.totalEvents}</div>
                <div className="text-sm text-gray-600">Total Events</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">{statistics.processedEvents}</div>
                <div className="text-sm text-gray-600">Processed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-red-600">{statistics.failedEvents}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">{statistics.replayedEvents}</div>
                <div className="text-sm text-gray-600">Replayed</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <Select value={serviceName} onValueChange={setServiceName}>
                <SelectTrigger>
                  <SelectValue placeholder="Service" />
                </SelectTrigger>
                <SelectContent>
                  {services.map(service => (
                    <SelectItem key={service} value={service}>{service}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={eventType} onValueChange={setEventType}>
                <SelectTrigger>
                  <SelectValue placeholder="Event Type" />
                </SelectTrigger>
                <SelectContent>
                  {eventTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(s => (
                    <SelectItem key={s} value={s}>{s}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                placeholder="User ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {startDate ? format(startDate, 'PPP') : 'Start Date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {endDate ? format(endDate, 'PPP') : 'End Date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
              <Button onClick={fetchEvents} disabled={loading}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Events Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Events</CardTitle>
              <Button variant="outline" onClick={fetchEvents} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Event ID</th>
                    <th className="text-left p-2">Type</th>
                    <th className="text-left p-2">Service</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Timestamp</th>
                    <th className="text-left p-2">User ID</th>
                    <th className="text-left p-2">Retry Count</th>
                  </tr>
                </thead>
                <tbody>
                  {events.map((event) => (
                    <tr key={event.id} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-mono text-sm">{event.eventId.substring(0, 8)}...</td>
                      <td className="p-2">{event.eventType}</td>
                      <td className="p-2">{event.serviceName}</td>
                      <td className="p-2">{getStatusBadge(event.status)}</td>
                      <td className="p-2">{new Date(event.timestamp).toLocaleString()}</td>
                      <td className="p-2">{event.userId || '-'}</td>
                      <td className="p-2">{event.retryCount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-600">
                Page {page + 1} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setPage(Math.max(0, page - 1))}
                  disabled={page === 0}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setPage(Math.min(totalPages - 1, page + 1))}
                  disabled={page >= totalPages - 1}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
