'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import SimpleProfileImage from '@/components/ui/SimpleProfileImage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import ModernLayout from '@/components/layout/ModernLayout';
import { SectionHeading } from '@/components/shared/SectionHeading';
import { AnimatedStats } from '@/components/shared/AnimatedStats';
import { GlowingChip } from '@/components/shared/GlowingChip';
import RecentNotifications from '@/components/dashboard/RecentNotifications';
import {
  User,
  Mail,
  Shield,
  Settings,
  Bell,
  Activity,
  Users,
  FileText,
  ClipboardList,
  FolderTree,
  Database,
  BarChart3,
  AlertTriangle
} from 'lucide-react';

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, loading } = useAuth();

  useEffect(() => {
    console.log('Dashboard: Auth state check:', { loading, isAuthenticated, user: !!user });

    if (!loading && !isAuthenticated) {
      console.log('Dashboard: Not authenticated, redirecting to login...');
      router.push('/auth/login');
    } else if (!loading && isAuthenticated && user) {
      console.log('Dashboard: Authenticated user:', user.email);
      console.log('Dashboard: User data from API:', user);
      console.log('Dashboard: User roles from API:', user.roles);
    }
  }, [loading, isAuthenticated, user, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  return (
    <ModernLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <SectionHeading size="md" align="left" className="mb-2">
              Dashboard
            </SectionHeading>
            <p className="text-gray-600 dark:text-gray-200">
              Welcome back, {user.name}! Here&apos;s what&apos;s happening with your platform today.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <Activity className="h-3 w-3 mr-1" />
              System Healthy
            </Badge>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-100">Total Users</CardTitle>
              <Users className="h-5 w-5 text-blue-200" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">1,234</div>
              <p className="text-xs text-blue-100 mt-1">
                +20.1% from last month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-100">Active Sessions</CardTitle>
              <Activity className="h-5 w-5 text-green-200" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">573</div>
              <p className="text-xs text-green-100 mt-1">
                +180.1% from last month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-100">Total Forms</CardTitle>
              <FileText className="h-5 w-5 text-purple-200" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">47</div>
              <p className="text-xs text-purple-100 mt-1">
                +5 new this month
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-100">Form Submissions</CardTitle>
              <ClipboardList className="h-5 w-5 text-orange-200" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">1,847</div>
              <p className="text-xs text-orange-100 mt-1">
                +127 this week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Animated Stats */}
        <Card className="p-6">
          <SectionHeading size="sm" className="mb-6">Platform Performance</SectionHeading>
          <AnimatedStats
            stats={[
              {
                num: 1234,
                subheading: "Total Users",
                icon: <Users className="h-8 w-8" />,
                color: "blue"
              },
              {
                num: 573,
                subheading: "Active Sessions",
                icon: <Activity className="h-8 w-8" />,
                color: "green"
              },
              {
                num: 1847,
                subheading: "Form Submissions",
                icon: <FileText className="h-8 w-8" />,
                color: "purple"
              },
              {
                num: 99.9,
                suffix: "%",
                decimals: 1,
                subheading: "System Uptime",
                icon: <Shield className="h-8 w-8" />,
                color: "orange"
              }
            ]}
          />
        </Card>

        {/* Recent Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Recent Notifications</span>
            </CardTitle>
            <CardDescription>
              Latest bulk notifications sent to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentNotifications />
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Quick Actions</span>
              </CardTitle>
              <CardDescription>
                Common administrative tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/users')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Manage Users</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">User administration and role management</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="blue" size="sm" glowIntensity="low">1,234 users</GlowingChip>
                  </div>
                </div>
                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/categories')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <FolderTree className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Categories</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">Hierarchical category management</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="green" size="sm" glowIntensity="low">847 categories</GlowingChip>
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/reference-data')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Database className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Reference Data</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">System reference data management</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="purple" size="sm" glowIntensity="low">12K records</GlowingChip>
                  </div>
                </div>
                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/analytics')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Analytics</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">View system analytics and reports</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="orange" size="sm" glowIntensity="low">Live data</GlowingChip>
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/exceptions')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Exceptions</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">Monitor system exceptions and errors</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="red" size="sm" glowIntensity="low">23 today</GlowingChip>
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/forms')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <FileText className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Forms</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">Manage dynamic forms and submissions</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="indigo" size="sm" glowIntensity="low">1,847 forms</GlowingChip>
                  </div>
                </div>

                <div
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => router.push('/events')}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Activity className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-black dark:text-white">Events</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-300">Monitor system events and replay functionality</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <GlowingChip color="orange" size="sm" glowIntensity="low">Live monitoring</GlowingChip>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>System Status</span>
              </CardTitle>
              <CardDescription>
                Current system health
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">API Status</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  Operational
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Database</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  Healthy
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cache</span>
                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                  Warning
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Storage</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  Available
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Account Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Your Account</span>
            </CardTitle>
            <CardDescription>
              Current user information and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Profile Photo */}
              <div className="flex flex-col items-center space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Profile Photo</label>
                  <div className="mt-2">
                    <SimpleProfileImage
                      src={user.imageUrl}
                      alt={user.name}
                      size="xl"
                    />
                  </div>
                </div>
              </div>

              {/* Basic Info */}
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-lg font-semibold">{user.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <p className="text-sm">{user.email}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Roles</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {user.roles?.map((role) => (
                      <Badge key={role} variant="outline" className="flex items-center space-x-1">
                        <Shield className="h-3 w-3" />
                        <span>{role}</span>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-green-600 font-medium">Online</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Last Login</label>
                  <p className="text-sm text-gray-600">Today at 2:30 PM</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </ModernLayout>
  );
}
