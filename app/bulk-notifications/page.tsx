'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Plus, Search, Eye, Users, MousePointer, Clock, Bell, Send, Edit, Trash2, FileText, MoreHorizontal, Loader2 } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';
import { LanguageCode } from '@/lib/types/category';
import ModernLayout from '@/components/layout/ModernLayout';

interface BulkNotification {
  id: string;
  title: string;
  message: string;
  titles: Record<string, string>;
  messages: Record<string, string>;
  status: string;
  priority: string;
  createdAt: string;
  targetUserCount: number;
  readCount: number;
  clickedCount: number;
  showInBell: boolean;
  sendEmail: boolean;
  sendPush: boolean;
}

interface CreateBulkNotificationRequest {
  titles: Record<string, string>;
  messages: Record<string, string>;
  notificationType: string;
  priority: string;
  showInBell: boolean;
  sendEmail: boolean;
  sendPush: boolean;
}

export default function BulkNotificationsPage() {
  const [notifications, setNotifications] = useState<BulkNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [language] = useState<LanguageCode>(LanguageCode.EN);



  // Create notification form state
  const [createForm, setCreateForm] = useState({
    translations: {
      titles: {
        "EN": "",
        "TR": ""
      } as Record<string, string>,
      messages: {
        "EN": "",
        "TR": ""
      } as Record<string, string>
    },
    notificationType: 'INFO',
    priority: 'NORMAL',
    showInBell: true,
    sendEmail: false,
    sendPush: false
  });

  // Action handlers
  const [viewingNotification, setViewingNotification] = useState<BulkNotification | null>(null);
  const [editingNotification, setEditingNotification] = useState<BulkNotification | null>(null);
  const [editForm, setEditForm] = useState({
    translations: {
      titles: {
        "EN": "",
        "TR": ""
      } as Record<string, string>,
      messages: {
        "EN": "",
        "TR": ""
      } as Record<string, string>
    },
    priority: 'NORMAL' as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
    showInBell: false,
    sendEmail: false,
    sendPush: false
  });

  const handleViewNotification = (notification: BulkNotification) => {
    setViewingNotification(notification);
  };

  const handleEditNotification = async (notification: BulkNotification) => {
    try {
      // Fetch full notification data with JSON translations
      const response = await fetch(`/api/notification-service/api/admin/notifications/bulk/${notification.id}?language=EN`);
      if (!response.ok) {
        throw new Error('Failed to fetch notification details');
      }

      const fullNotification = await response.json();
      setEditingNotification(fullNotification);

      // Pre-populate form with JSON translations data
      setEditForm({
        translations: {
          titles: {
            "EN": fullNotification.titles?.EN || "",
            "TR": fullNotification.titles?.TR || ""
          },
          messages: {
            "EN": fullNotification.messages?.EN || "",
            "TR": fullNotification.messages?.TR || ""
          }
        },
        priority: fullNotification.priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
        showInBell: fullNotification.showInBell,
        sendEmail: fullNotification.sendEmail,
        sendPush: fullNotification.sendPush
      });
    } catch {
      console.error('Error fetching notification details');
      alert('Failed to load notification details');
    }
  };

  const handleEditSubmit = async () => {
    if (!editingNotification) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/notification-service/api/admin/notifications/bulk/${editingNotification.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationType: 'INFO',
          priority: editForm.priority,
          titles: editForm.translations.titles,
          messages: editForm.translations.messages,
          showInBell: editForm.showInBell,
          sendEmail: editForm.sendEmail,
          sendPush: editForm.sendPush
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('Notification updated successfully');
          setEditingNotification(null);
          fetchNotifications(); // Refresh the list
        } else {
          alert('Failed to update notification: ' + result.error);
        }
      } else {
        alert('Failed to update notification');
      }
    } catch {
      console.error('Error updating notification');
      alert('Failed to update notification');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    if (confirm('Are you sure you want to delete this notification?')) {
      try {
        const response = await fetch(`/api/notification-service/api/admin/notifications/bulk/${notificationId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            alert('Notification deleted successfully');
            fetchNotifications(); // Refresh the list
          } else {
            alert('Failed to delete notification: ' + result.error);
          }
        } else {
          alert('Failed to delete notification');
        }
      } catch {
        console.error('Error deleting notification');
        alert('Failed to delete notification');
      }
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [currentPage, searchTerm, statusFilter, priorityFilter, language]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: '10',
        language: language
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (priorityFilter !== 'all') params.append('priority', priorityFilter);

      const response = await fetch(`/api/notification-service/api/admin/notifications/bulk?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data.content || []);
      setTotalPages(data.totalPages || 0);
    } catch {
      console.error('Error fetching notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNotification = async () => {
    setIsCreating(true);
    try {
      const request: CreateBulkNotificationRequest = {
        titles: createForm.translations.titles,
        messages: createForm.translations.messages,
        notificationType: createForm.notificationType,
        priority: createForm.priority,
        showInBell: createForm.showInBell,
        sendEmail: createForm.sendEmail,
        sendPush: createForm.sendPush
      };

      const response = await fetch('/api/notification-service/api/admin/notifications/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('Failed to create notification');
      }

      // Reset form and close dialog
      setCreateForm({
        translations: {
          titles: {
            "EN": "",
            "TR": ""
          },
          messages: {
            "EN": "",
            "TR": ""
          }
        },
        notificationType: 'INFO',
        priority: 'NORMAL',
        showInBell: true,
        sendEmail: false,
        sendPush: false
      });
      setIsCreateDialogOpen(false);

      // Refresh notifications list
      fetchNotifications();
    } catch {
      console.error('Error creating notification');
      alert('Error creating notification. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'sent':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <ModernLayout>
      <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white">Bulk Notifications</h1>
          <p className="text-black dark:text-white mt-1">Manage and send notifications to all users</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Create Bulk Notification</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Bulk Notification</DialogTitle>
              <DialogDescription>
                Send a notification to all active users in the system
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Title Fields */}
              <div>
                <Label className="text-base font-medium">Titles (JSON Format)</Label>
                <div className="mt-2">
                  <Label htmlFor="titles-json">
                    Titles JSON (All Languages)
                  </Label>
                  <Textarea
                    id="titles-json"
                    value={JSON.stringify(createForm.translations.titles, null, 2)}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        setCreateForm(prev => ({
                          ...prev,
                          translations: {
                            ...prev.translations,
                            titles: parsed
                          }
                        }));
                      } catch {
                        // Invalid JSON, don't update
                      }
                    }}
                    placeholder={`{\n  "EN": "English title",\n  "TR": "Turkish title",\n  "DE": "German title"\n}`}
                    rows={6}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter titles as JSON object with language codes as keys
                  </p>
                </div>
              </div>

              {/* Message Fields */}
              <div>
                <Label className="text-base font-medium">Messages (JSON Format)</Label>
                <div className="mt-2">
                  <Label htmlFor="messages-json">
                    Messages JSON (All Languages)
                  </Label>
                  <Textarea
                    id="messages-json"
                    value={JSON.stringify(createForm.translations.messages, null, 2)}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        setCreateForm(prev => ({
                          ...prev,
                          translations: {
                            ...prev.translations,
                            messages: parsed
                          }
                        }));
                      } catch {
                        // Invalid JSON, don't update
                      }
                    }}
                    placeholder={`{\n  "EN": "English message",\n  "TR": "Turkish message",\n  "DE": "German message"\n}`}
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter messages as JSON object with language codes as keys
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="notificationType">Type</Label>
                  <Select value={createForm.notificationType} onValueChange={(value) => setCreateForm(prev => ({ ...prev, notificationType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INFO">Info</SelectItem>
                      <SelectItem value="WARNING">Warning</SelectItem>
                      <SelectItem value="ERROR">Error</SelectItem>
                      <SelectItem value="SUCCESS">Success</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={createForm.priority} onValueChange={(value) => setCreateForm(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="NORMAL">Normal</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="showInBell"
                    checked={createForm.showInBell}
                    onCheckedChange={(checked) => setCreateForm(prev => ({ ...prev, showInBell: checked }))}
                  />
                  <Label htmlFor="showInBell">Show in Bell</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="sendEmail"
                    checked={createForm.sendEmail}
                    onCheckedChange={(checked) => setCreateForm(prev => ({ ...prev, sendEmail: checked }))}
                  />
                  <Label htmlFor="sendEmail">Send Email</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="sendPush"
                    checked={createForm.sendPush}
                    onCheckedChange={(checked) => setCreateForm(prev => ({ ...prev, sendPush: checked }))}
                  />
                  <Label htmlFor="sendPush">Send Push</Label>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateNotification}
                  disabled={isCreating}
                  className="flex items-center space-x-2"
                >
                  {isCreating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                  <span>{isCreating ? 'Creating...' : 'Send Notification'}</span>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <div className="space-y-4">
        {loading ? (
          [...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="flex space-x-4">
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : notifications.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-gray-500">
                <Bell className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>No bulk notifications found</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          notifications.map((notification) => (
            <Card key={notification.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{notification.title}</h3>
                      <Badge className={getStatusColor(notification.status)} variant="outline">
                        {notification.status}
                      </Badge>
                      <Badge className={getPriorityColor(notification.priority)} variant="outline">
                        {notification.priority}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 dark:text-white mb-4 whitespace-pre-wrap">{notification.message}</p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-white">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{notification.targetUserCount} users</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{notification.readCount} read</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MousePointer className="h-4 w-4" />
                        <span>{notification.clickedCount} clicked</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions Dropdown */}
                  <div className="flex-shrink-0">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewNotification(notification)}>
                          <FileText className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditNotification(notification)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteNotification(notification.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* View Notification Dialog */}
      <Dialog open={!!viewingNotification} onOpenChange={() => setViewingNotification(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Notification Details</DialogTitle>
            <DialogDescription>
              View complete notification information
            </DialogDescription>
          </DialogHeader>
          {viewingNotification && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">Title</Label>
                <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                  {viewingNotification.title}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">Message</Label>
                <p className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md whitespace-pre-wrap">
                  {viewingNotification.message}
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Status</Label>
                  <p className="mt-1 text-sm">
                    <Badge className={getPriorityColor(viewingNotification.status)}>
                      {viewingNotification.status}
                    </Badge>
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Priority</Label>
                  <p className="mt-1 text-sm">
                    <Badge className={getPriorityColor(viewingNotification.priority)}>
                      {viewingNotification.priority}
                    </Badge>
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Target Users</Label>
                  <p className="mt-1 text-sm text-gray-900">{viewingNotification.targetUserCount || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Read Count</Label>
                  <p className="mt-1 text-sm text-gray-900">{viewingNotification.readCount || 0}</p>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Show in Bell</Label>
                  <p className="mt-1 text-sm text-gray-900">{viewingNotification.showInBell ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Send Email</Label>
                  <p className="mt-1 text-sm text-gray-900">{viewingNotification.sendEmail ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Send Push</Label>
                  <p className="mt-1 text-sm text-gray-900">{viewingNotification.sendPush ? 'Yes' : 'No'}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">Created At</Label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(viewingNotification.createdAt).toLocaleString()}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Notification Dialog */}
      <Dialog open={!!editingNotification} onOpenChange={() => setEditingNotification(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Bulk Notification</DialogTitle>
            <DialogDescription>
              Update notification details and translations
            </DialogDescription>
          </DialogHeader>
          {editingNotification && (
            <div className="space-y-6">
              {/* Titles Section */}
              <div>
                <Label className="text-base font-medium">Titles (JSON Format)</Label>
                <div className="mt-2">
                  <Label htmlFor="edit-titles-json">
                    Titles JSON (All Languages)
                  </Label>
                  <Textarea
                    id="edit-titles-json"
                    value={JSON.stringify(editForm.translations.titles, null, 2)}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        setEditForm(prev => ({
                          ...prev,
                          translations: {
                            ...prev.translations,
                            titles: parsed
                          }
                        }));
                      } catch {
                        // Invalid JSON, don't update state
                      }
                    }}
                    placeholder={`{\n  "EN": "English title",\n  "TR": "Turkish title",\n  "DE": "German title"\n}`}
                    rows={6}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter titles as JSON object with language codes as keys
                  </p>
                </div>
              </div>

              {/* Messages Section */}
              <div>
                <Label className="text-base font-medium">Messages (JSON Format)</Label>
                <div className="mt-2">
                  <Label htmlFor="edit-messages-json">
                    Messages JSON (All Languages)
                  </Label>
                  <Textarea
                    id="edit-messages-json"
                    value={JSON.stringify(editForm.translations.messages, null, 2)}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        setEditForm(prev => ({
                          ...prev,
                          translations: {
                            ...prev.translations,
                            messages: parsed
                          }
                        }));
                      } catch {
                        // Invalid JSON, don't update state
                      }
                    }}
                    placeholder={`{\n  "EN": "English message",\n  "TR": "Turkish message",\n  "DE": "German message"\n}`}
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter messages as JSON object with language codes as keys
                  </p>
                </div>
              </div>

              {/* Settings Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="edit-priority">Priority</Label>
                  <Select
                    value={editForm.priority}
                    onValueChange={(value: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT') =>
                      setEditForm(prev => ({ ...prev, priority: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="NORMAL">Normal</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="edit-show-in-bell"
                      checked={editForm.showInBell}
                      onChange={(e) => setEditForm(prev => ({ ...prev, showInBell: e.target.checked }))}
                      className="rounded"
                    />
                    <Label htmlFor="edit-show-in-bell">Show in Bell</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="edit-send-email"
                      checked={editForm.sendEmail}
                      onChange={(e) => setEditForm(prev => ({ ...prev, sendEmail: e.target.checked }))}
                      className="rounded"
                    />
                    <Label htmlFor="edit-send-email">Send Email</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="edit-send-push"
                      checked={editForm.sendPush}
                      onChange={(e) => setEditForm(prev => ({ ...prev, sendPush: e.target.checked }))}
                      className="rounded"
                    />
                    <Label htmlFor="edit-send-push">Send Push</Label>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setEditingNotification(null)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEditSubmit}
                  disabled={isUpdating}
                  className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                >
                  {isUpdating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Edit className="h-4 w-4" />
                  )}
                  <span>{isUpdating ? 'Updating...' : 'Update Notification'}</span>
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
            disabled={currentPage === 0}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-gray-600">
            Page {currentPage + 1} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
            disabled={currentPage === totalPages - 1}
          >
            Next
          </Button>
        </div>
      )}
      </div>
    </ModernLayout>
  );
}
