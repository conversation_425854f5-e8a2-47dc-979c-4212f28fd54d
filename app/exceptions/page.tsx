'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Eye, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { ExceptionResponse, HttpStatus } from '@/lib/types/exception';
import { LanguageCode } from '@/lib/types/common';
import { exceptionService } from '@/lib/exception-service';
import { ExceptionForm } from '@/components/exceptions/ExceptionForm';
import { ExceptionMessageDialog } from '@/components/exceptions/ExceptionMessageDialog';
import ModernLayout from '@/components/layout/ModernLayout';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

export default function ExceptionsPage() {
  const [exceptions, setExceptions] = useState<ExceptionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingException, setEditingException] = useState<ExceptionResponse | null>(null);
  const [showMessageDialog, setShowMessageDialog] = useState(false);
  const [selectedExceptionCode, setSelectedExceptionCode] = useState<string>('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [sortBy, setSortBy] = useState('id');
  const [sortDir, setSortDir] = useState('desc');

  const { toast } = useToast();

  useEffect(() => {
    loadExceptions();
  }, []);

  // Load exceptions when search, filter, or pagination changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadExceptions(0); // Reset to first page when search changes
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, pageSize]);

  // Load exceptions when page changes
  useEffect(() => {
    if (currentPage > 0) { // Don't load on initial load
      loadExceptions(currentPage);
    }
  }, [currentPage]);

  const loadExceptions = async (page = currentPage, search = searchTerm) => {
    try {
      setLoading(true);

      const result = await exceptionService.getPaginatedExceptions(
        page,
        pageSize,
        sortBy,
        sortDir,
        search
      );

      setExceptions(result.content || []);
      setTotalPages(result.totalPages || 0);
      setTotalElements(result.totalElements || 0);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading exceptions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load exceptions',
        variant: 'destructive',
      });
      setExceptions([]);
    } finally {
      setLoading(false);
    }
  };

  // Pagination navigation
  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(0); // Reset to first page
  };

  const handleCreateException = () => {
    setEditingException(null);
    setShowForm(true);
  };

  const handleEditException = (exception: ExceptionResponse) => {
    setEditingException(exception);
    setShowForm(true);
  };

  const handleDeleteException = async (exception: ExceptionResponse) => {
    if (!confirm(`Are you sure you want to delete exception "${exception.exceptionCode}"?`)) {
      return;
    }

    try {
      await exceptionService.deleteException(exception.id);
      await loadExceptions();
      toast({
        title: 'Success',
        description: 'Exception deleted successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete exception',
        variant: 'destructive',
      });
    }
  };

  const handleFormSubmit = async () => {
    setShowForm(false);
    setEditingException(null);
    await loadExceptions();
    toast({
      title: 'Success',
      description: editingException ? 'Exception updated successfully' : 'Exception created successfully',
    });
  };

  const handleViewMessages = (exceptionCode: string) => {
    setSelectedExceptionCode(exceptionCode);
    setShowMessageDialog(true);
  };

  const getHttpStatusBadgeVariant = (status: HttpStatus) => {
    switch (status) {
      case HttpStatus.OK:
      case HttpStatus.CREATED:
        return 'default';
      case HttpStatus.BAD_REQUEST:
      case HttpStatus.UNAUTHORIZED:
      case HttpStatus.FORBIDDEN:
        return 'secondary';
      case HttpStatus.NOT_FOUND:
      case HttpStatus.CONFLICT:
        return 'destructive';
      case HttpStatus.INTERNAL_SERVER_ERROR:
      case HttpStatus.SERVICE_UNAVAILABLE:
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getLanguageCount = (messages: Record<LanguageCode, string>): number => {
    if (!messages || typeof messages !== 'object') return 0;
    return Object.keys(messages).length;
  };

  const getPrimaryMessage = (messages: Record<LanguageCode, string>): string => {
    if (!messages || typeof messages !== 'object') return 'No message available';
    // Try English first, then Turkish, then any available
    return messages[LanguageCode.EN] ||
           messages[LanguageCode.TR] ||
           Object.values(messages)[0] ||
           'No message available';
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <ModernLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">Exception Management</h1>
            <p className="mt-2 text-black dark:text-white">
              Manage application exceptions and error messages
            </p>
          </div>
          <Button onClick={handleCreateException}>
            <Plus className="h-4 w-4 mr-2" />
            Add Exception
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search exceptions by code or message..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{totalElements}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Total Exceptions</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {exceptions.filter(e => e.httpStatus === HttpStatus.INTERNAL_SERVER_ERROR).length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Server Errors</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {exceptions.filter(e => e.httpStatus === HttpStatus.BAD_REQUEST).length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Client Errors</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {Math.round(exceptions.reduce((sum, e) => sum + getLanguageCount(e.messages), 0) / exceptions.length || 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Avg Languages</div>
          </CardContent>
        </Card>
      </div>

      {/* Exceptions List */}
      <div className="grid gap-4">
        {exceptions.map((exception) => (
          <Card key={exception.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold">{exception.exceptionCode}</h3>
                    <Badge variant={getHttpStatusBadgeVariant(exception.httpStatus)}>
                      {exceptionService.getHttpStatusDisplayName(exception.httpStatus)}
                    </Badge>
                    <Badge variant="outline">
                      {getLanguageCount(exception.messages)} languages
                    </Badge>
                  </div>
                  <p className="text-gray-600 mb-2">
                    {getPrimaryMessage(exception.messages)}
                  </p>
                  <div className="text-sm text-gray-500">
                    ID: {exception.id}
                    {exception.createdAt && ` • Created: ${new Date(exception.createdAt).toLocaleDateString()}`}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewMessages(exception.exceptionCode)}
                  >
                    <MessageSquare className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditException(exception)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteException(exception)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {exceptions.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-gray-500">
              {searchTerm ? 'No exceptions found matching your search.' : 'No exceptions found.'}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-600">
            Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalElements)} of {totalElements} exceptions
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 0}
            >
              Previous
            </Button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum + 1}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages - 1}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Exception Form Dialog */}
      {showForm && (
        <ExceptionForm
          exception={editingException}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingException(null);
          }}
        />
      )}

      {/* Exception Message Dialog */}
      {showMessageDialog && (
        <ExceptionMessageDialog
          exceptionCode={selectedExceptionCode}
          onClose={() => {
            setShowMessageDialog(false);
            setSelectedExceptionCode('');
          }}
        />
      )}
      </div>
    </ModernLayout>
  );
}
