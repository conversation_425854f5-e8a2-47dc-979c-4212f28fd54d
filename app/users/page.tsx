'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import ModernLayout from '@/components/layout/ModernLayout';
import ProfileImage from '@/components/ui/ProfileImage';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  UserX,
  UserCheck,
  Shield,
  Mail,
  Calendar,
} from 'lucide-react';
import userService, { User, UserSearchParams } from '@/lib/user-service';
import UserRoleModal from '@/components/users/UserRoleModal';
import UserStatusModal from '@/components/users/UserStatusModal';
import { MultiSelect } from '@/components/ui/multi-select';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

export default function UsersPage() {
  const { isAuthenticated, loading, user: currentUser } = useAuth();
  const router = useRouter();

  const [users, setUsers] = useState<User[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [roleModalOpen, setRoleModalOpen] = useState(false);
  const [statusModalOpen, setStatusModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [availableRoles, setAvailableRoles] = useState<string[]>([]);
  const [statusSelectOpen, setStatusSelectOpen] = useState(false);
  const [rolesSelectOpen, setRolesSelectOpen] = useState(false);

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [loading, isAuthenticated, router]);

  useEffect(() => {
    const loadRoles = async () => {
      try {
        const roles = await userService.getAvailableRoles();
        setAvailableRoles(roles);
      } catch (error) {
        console.error('Error loading roles:', error);
      }
    };
    loadRoles();
  }, []);

  const loadUsers = async () => {
    try {
      setLoadingUsers(true);
      setError('');

      const params: UserSearchParams = {
        page: currentPage - 1,
        size: pageSize,
        search: searchTerm || undefined,
        roles: selectedRoles.length > 0 ? selectedRoles : undefined,
        active: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined,
        sortBy: 'createdAt',
        sortDirection: 'desc',
      };

      const response = await userService.getUsers(params);
      console.log('Users data from backend:', response.users);
      response.users.forEach(user => {
        console.log(`User ${user.name}: imageUrl = ${user.imageUrl}`);
      });
      setUsers(response.users);
      setTotalCount(response.totalCount);
    } catch (error: any) {
      console.error('Error loading users:', error);
      setError(error.response?.data?.message || 'Failed to load users');
    } finally {
      setLoadingUsers(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadUsers();
    }
  }, [isAuthenticated, currentPage, searchTerm, selectedRoles, selectedStatus]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleRoleFilter = (roles: string[]) => {
    setSelectedRoles(roles);
    setCurrentPage(1);
  };

  const handleRoleUpdate = async (userId: string, newRoles: string[]) => {
    try {
      setError('');
      await userService.updateUserRoles(userId, newRoles);
      setSuccess('User roles updated successfully');
      setRoleModalOpen(false);
      setSelectedUser(null);
      await loadUsers();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error: any) {
      console.error('Error updating user roles:', error);
      setError(error.response?.data?.message || 'Failed to update user roles');
    }
  };

  const handleStatusUpdate = async (userId: string, active: boolean) => {
    try {
      setError('');
      await userService.updateUserStatus(userId, active);
      setSuccess(`User ${active ? 'activated' : 'deactivated'} successfully`);
      setStatusModalOpen(false);
      setSelectedUser(null);
      await loadUsers();
      setTimeout(() => setSuccess(''), 3000);
    } catch (error: any) {
      console.error('Error updating user status:', error);
      setError(error.response?.data?.message || 'Failed to update user status');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'destructive';
      case 'moderator':
        return 'default';
      case 'user':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (loading || !isAuthenticated) {
    return <LoadingSpinner />;
  }

  // Show fullscreen spinner when loadingUsers is true
  if (loadingUsers) {
    return <LoadingSpinner />;
  }

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
      <ModernLayout>
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">User Management</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">Manage user accounts, roles, and permissions</p>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                {totalCount} Total Users
              </Badge>
            </div>
          </div>

          {/* Alert Messages */}
          {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
          )}
          {success && (
              <Alert className="border-green-200 bg-green-50">
                <AlertDescription className="text-green-800">{success}</AlertDescription>
              </Alert>
          )}

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-10"
                  />
                </div>
                <MultiSelect
                    options={availableRoles.map(role => ({ value: role, label: role }))}
                    value={selectedRoles}
                    onChange={handleRoleFilter}
                    placeholder="Select roles..."
                    className="min-w-[200px]"
                    isOpen={rolesSelectOpen}
                    setIsOpen={(open) => {
                      setRolesSelectOpen(open);
                      if (open) setStatusSelectOpen(false);
                    }}
                />
                <Select
                  value={selectedStatus || 'all'}
                  onValueChange={(val) => {
                    setSelectedStatus(val);
                    setCurrentPage(1);
                  }}
                  open={statusSelectOpen}
                  onOpenChange={(open) => {
                    setStatusSelectOpen(open);
                    if (open) setRolesSelectOpen(false);
                  }}
                >
                  <SelectTrigger className="min-w-[180px] text-left cursor-pointer">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedRoles([]);
                      setSelectedStatus('all');
                      setCurrentPage(1);
                    }}
                >
                  Clear Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Kullanıcı Tablosu */}
          <Card>
            <CardHeader>
              <CardTitle>Users ({totalCount})</CardTitle>
              <CardDescription>
                Showing {users.length} of {totalCount} users
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingUsers ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">Loading users...</span>
                  </div>
              ) : users.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No users found matching your criteria.
                  </div>
              ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">User</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Roles</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Created</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Last Login</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">Actions</th>
                      </tr>
                      </thead>
                      <tbody>
                      {users.map((user) => (
                          <tr key={user.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <td className="py-4 px-4">
                              <div className="flex items-center gap-3">
                                <ProfileImage
                                    src={user.imageUrl}
                                    alt={user.name}
                                    size="md"
                                />
                                <div className="flex flex-col">
                                  <button
                                      onClick={() => router.push(`/users/${user.id}`)}
                                      className="font-medium text-blue-600 hover:text-blue-800 text-left focus:outline-none"
                                      style={{ background: 'none', border: 'none', padding: 0, cursor: 'pointer' }}
                                  >
                                    {user.name}
                                  </button>
                                  <div className="text-sm text-gray-500 flex items-center gap-1">
                                    <Mail className="h-3 w-3" />
                                    {user.email}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="flex flex-wrap gap-1">
                                {user.roles.map((role) => (
                                    <Badge
                                        key={role}
                                        variant={getRoleBadgeVariant(role)}
                                        className="text-xs"
                                    >
                                      {role}
                                    </Badge>
                                ))}
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <Badge
                                  variant={user.active ? 'default' : 'secondary'}
                                  className={
                                    user.active
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                  }
                              >
                                {user.active ? 'Active' : 'Inactive'}
                              </Badge>
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600 dark:text-gray-200">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(user.createdAt ?? '')}
                              </div>
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600 dark:text-gray-200">
                              {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
                            </td>
                            <td className="py-4 px-4 align-middle">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedUser(user);
                                      setRoleModalOpen(true);
                                    }}
                                    disabled={user.id === currentUser?.id}
                                >
                                  <Shield className="h-4 w-4 mr-1" />
                                  Roles
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedUser(user);
                                      setStatusModalOpen(true);
                                    }}
                                    disabled={user.id === currentUser?.id}
                                    className={
                                      user.active
                                          ? 'text-red-600 hover:text-red-700'
                                          : 'text-green-600 hover:text-green-700'
                                    }
                                >
                                  {user.active ? (
                                      <>
                                        <UserX className="h-4 w-4 mr-1" />
                                        Deactivate
                                      </>
                                  ) : (
                                      <>
                                        <UserCheck className="h-4 w-4 mr-1" />
                                        Activate
                                      </>
                                  )}
                                </Button>
                              </div>
                            </td>
                          </tr>
                      ))}
                      </tbody>
                    </table>
                  </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-gray-600">
                      Showing {((currentPage - 1) * pageSize) + 1} to{' '}
                      {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                          disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                      <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                          disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
              )}
            </CardContent>
          </Card>


          {/* Modals */}
          {selectedUser && (
              <>
                <UserRoleModal
                    isOpen={roleModalOpen}
                    onClose={() => {
                      setRoleModalOpen(false);
                      setSelectedUser(null);
                    }}
                    user={selectedUser}
                    availableRoles={availableRoles}
                    onUpdate={handleRoleUpdate}
                />
                <UserStatusModal
                    isOpen={statusModalOpen}
                    onClose={() => {
                      setStatusModalOpen(false);
                      setSelectedUser(null);
                    }}
                    user={selectedUser}
                    onUpdate={handleStatusUpdate}
                />
              </>
          )}
        </div>
      </ModernLayout>
  );
}
