'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  ArrowLeft, 
  Save,
  User as UserIcon,
  Shield
} from 'lucide-react';
import { User, userService } from '@/lib/user-service';
import { UserProfileEdit } from '@/components/users/UserProfileEdit';
import { useToast } from '@/contexts/ToastContext';

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;
  const toast = useToast();
  
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'account' | 'profile'>('profile');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    roles: [] as string[],
    active: true,
    emailVerified: false
  });

  const availableRoles = ['USER', 'ADMIN', 'MODERATOR'];

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await userService.getUserById(userId);
      setUser(userData);
      setFormData({
        name: userData.name,
        email: userData.email,
        roles: userData.roles,
        active: userData.active,
        emailVerified: userData.emailVerified || false
      });
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleRoleChange = (role: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      roles: checked 
        ? [...prev.roles, role]
        : prev.roles.filter(r => r !== role)
    }));
  };



  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-6">
          <div className="flex items-center space-x-4">
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <UserIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">User not found</h2>
          <p className="text-gray-600 mb-4">The user you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/users')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6 px-4 md:px-8 lg:px-16">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push(`/users/${userId}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to User
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit User</h1>
            <p className="text-gray-600">{user.email}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/users/${userId}`)}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (activeTab === 'profile') {
                // Trigger save from UserProfileEdit component
                const saveButton = document.querySelector('[data-save-profile]') as HTMLButtonElement;
                if (saveButton) {
                  saveButton.click();
                }
              } else if (activeTab === 'account') {
                // Trigger save from Account Settings
                const saveButton = document.querySelector('[data-save-account]') as HTMLButtonElement;
                if (saveButton) {
                  saveButton.click();
                }
              }
            }}
          >
            <Save className="h-4 w-4 mr-2" />
            {activeTab === 'account' ? 'Save Account Settings' : 'Update Profile'}
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('account')}
            className={`border-b-2 py-2 px-1 text-sm font-medium ${
              activeTab === 'account'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Account Settings
          </button>
          <button
            onClick={() => setActiveTab('profile')}
            className={`border-b-2 py-2 px-1 text-sm font-medium ${
              activeTab === 'profile'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Profile Information
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'account' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <UserIcon className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
            <CardDescription>
              Basic user account information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter full name"
                disabled // Name editing might require special handling
              />
              <p className="text-xs text-gray-500">
                Name editing is currently disabled for security reasons
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter email address"
                disabled // Email editing might require special handling
              />
              <p className="text-xs text-gray-500">
                Email editing is currently disabled for security reasons
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="active"
                checked={formData.active}
                onCheckedChange={(checked) => handleInputChange('active', checked)}
              />
              <Label htmlFor="active">Account Active</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="emailVerified"
                checked={formData.emailVerified}
                onCheckedChange={(checked) => handleInputChange('emailVerified', checked)}
                disabled // Email verification should be handled by system
              />
              <Label htmlFor="emailVerified">Email Verified</Label>
            </div>
          </CardContent>
        </Card>

        {/* Roles & Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Roles & Permissions</span>
            </CardTitle>
            <CardDescription>
              Manage user roles and access permissions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>User Roles</Label>
              {availableRoles.map((role) => (
                <div key={role} className="flex items-center space-x-2">
                  <Checkbox
                    id={`role-${role}`}
                    checked={formData.roles.includes(role)}
                    onCheckedChange={(checked) => handleRoleChange(role, checked as boolean)}
                  />
                  <Label htmlFor={`role-${role}`} className="flex items-center space-x-2">
                    <span>{role}</span>
                    <Badge variant="outline" className="text-xs">
                      {role === 'ADMIN' && 'Full Access'}
                      {role === 'MODERATOR' && 'Content Management'}
                      {role === 'USER' && 'Basic Access'}
                    </Badge>
                  </Label>
                </div>
              ))}
            </div>

            <div className="pt-4 border-t">
              <Label className="text-sm font-medium">Current Roles</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.roles.map((role) => (
                  <Badge key={role} variant="default">
                    {role}
                  </Badge>
                ))}
                {formData.roles.length === 0 && (
                  <span className="text-sm text-gray-500">No roles assigned</span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Status */}
        <Card>
          <CardHeader>
            <CardTitle>Account Status</CardTitle>
            <CardDescription>
              Current account status and metadata
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-gray-500">Status</Label>
                <p className="font-medium">
                  {formData.active ? (
                    <Badge variant="default">Active</Badge>
                  ) : (
                    <Badge variant="destructive">Inactive</Badge>
                  )}
                </p>
              </div>
              <div>
                <Label className="text-gray-500">Email Status</Label>
                <p className="font-medium">
                  {formData.emailVerified ? (
                    <Badge variant="default">Verified</Badge>
                  ) : (
                    <Badge variant="secondary">Unverified</Badge>
                  )}
                </p>
              </div>
              {user.createdAt && (
                <div>
                  <Label className="text-gray-500">Created</Label>
                  <p className="font-medium">{new Date(user.createdAt).toLocaleDateString()}</p>
                </div>
              )}
              {user.lastLoginAt && (
                <div>
                  <Label className="text-gray-500">Last Login</Label>
                  <p className="font-medium">{new Date(user.lastLoginAt).toLocaleDateString()}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Save Changes */}
        <Card>
          <CardHeader>
            <CardTitle>Save Changes</CardTitle>
            <CardDescription>
              Save the account settings changes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              className="w-full"
              data-save-account
              onClick={async () => {
                try {
                  // Update user roles if changed
                  if (JSON.stringify(formData.roles.sort()) !== JSON.stringify(user.roles.sort())) {
                    await userService.updateUserRoles(userId, formData.roles);
                  }

                  // Update user status if changed
                  if (formData.active !== user.active) {
                    await userService.updateUserStatus(userId, formData.active);
                  }

                  toast.success('Account settings updated successfully');

                  // Refresh user data
                  const updatedUser = await userService.getUserById(userId);
                  setUser(updatedUser);
                  setFormData({
                    name: updatedUser.name,
                    email: updatedUser.email,
                    roles: updatedUser.roles,
                    active: updatedUser.active,
                    emailVerified: updatedUser.emailVerified || false
                  });
                } catch (error) {
                  console.error('Error updating account settings:', error);
                  toast.error('Failed to update account settings');
                }
              }}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Account Settings
            </Button>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Danger Zone</CardTitle>
            <CardDescription>
              Irreversible and destructive actions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              variant="destructive"
              className="w-full"
              onClick={() => {
                if (confirm(`Are you sure you want to delete user ${user.email}? This action cannot be undone.`)) {
                  userService.deleteUser(userId).then(() => {
                    toast.success('User deleted successfully');
                    router.push('/users');
                  }).catch(() => {
                    toast.error('Failed to delete user');
                  });
                }
              }}
            >
              Delete User Account
            </Button>
          </CardContent>
        </Card>
        </div>
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <UserProfileEdit
          userId={userId}
          onSave={() => {
            toast.showDialog({
              title: 'Profile Updated Successfully',
              description: 'The user profile has been updated successfully. Would you like to view the user details?',
              confirmText: 'View Profile',
              cancelText: 'Stay Here',
              onConfirm: () => {
                router.push(`/users/${userId}`);
              },
              onCancel: () => {
                // Stay on edit page
              }
            });
          }}
        />
      )}
    </div>
  );
}
