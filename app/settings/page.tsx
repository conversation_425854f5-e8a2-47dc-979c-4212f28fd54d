"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ModernLayout from '@/components/layout/ModernLayout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/contexts/ToastContext';
import { useDarkMode } from '@/contexts/DarkModeContext';

const languages = [
  { label: 'English', value: 'en' },
  { label: 'Türkçe', value: 'tr' },
  { label: 'Deutsch', value: 'de' },
  { label: 'Español', value: 'es' },
];

export default function SettingsPage() {
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const { isDarkMode, setDarkMode } = useDarkMode();
  const router = useRouter();
  const toast = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success('Preferences saved successfully!', 'Settings');
    setTimeout(() => {
      router.push('/dashboard');
    }, 1200);
  };

  return (
    <ModernLayout>
      <div className="max-w-3xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 pt-4 pb-2">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Manage your appearance and language preferences</p>
          </div>
        </div>
        {/* Settings Card */}
        <Card className="w-full max-w-md mx-auto p-8 shadow bg-white/90 dark:bg-gray-800/90 border border-gray-200 dark:border-gray-700 rounded-2xl">
          <form className="space-y-8" onSubmit={handleSubmit}>
            {/* Language Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Language</label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {/* Layout Style */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">Dark Mode</span>
              <Switch checked={isDarkMode} onCheckedChange={setDarkMode} />
            </div>
            {/* Placeholder for more settings */}
            <div className="text-xs text-gray-400 dark:text-gray-500 text-center pt-2">
              More personalization options coming soon...
            </div>
            <Button type="submit" className="w-full mt-4">Save Preferences</Button>
          </form>
        </Card>
      </div>
    </ModernLayout>
  );
} 