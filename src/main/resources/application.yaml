spring:
  application:
    name: media-microservice
  config:
    import: "optional:configserver:"
  cloud:
    discovery:
      enabled: true
      service-id: config-server
    fail-fast: true
  profiles:
    active: dev

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
server:
  port: 8090

exception-service:
  url: http://localhost:8080/exception-service/api/v1/exceptions

common-service:
  exception-service-url: ${exception-service.url}
