spring:
  application:
    name: reference-data-microservice
  config:
    import: "optional:configserver:"
  cloud:
    discovery:
      enabled: true
      service-id: config-server
    fail-fast: true
  profiles:
    active: dev

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true

server:
  port: 8112

# Logging configuration
logging:
  level:
    root: INFO
    "[com.lookforx.referencedataservice]": INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Management endpoints for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,caches
  endpoint:
    caches:
      enabled: true
