spring:
  application:
    name: notification-microservice
  config:
    import: "optional:configserver:"
  cloud:
    config:
      discovery:
        enabled: true
        service-id: config-server
      fail-fast: false
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true

server:
  port: 8105

logging:
  level:
    com.lookforx.notificationservice.service.OptimizedBulkNotificationService: DEBUG