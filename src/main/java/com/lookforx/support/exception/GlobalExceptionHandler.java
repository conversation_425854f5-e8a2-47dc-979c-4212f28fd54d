package com.lookforx.support.exception;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ErrorResponse;
import com.lookforx.common.dto.ExceptionMessageResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

@RestControllerAdvice
@RequiredArgsConstructor
@Slf4j
public class GlobalExceptionHandler {

    private final ExceptionServiceClient exceptionClient;

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(
            MethodArgumentNotValidException ex,
            HttpServletRequest request
    ) {
        String lang = resolveLanguage(request);

        // 1) Collect field‐level errors
        Map<String, String> fieldErrors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(err -> {
            String field = ((FieldError) err).getField();
            fieldErrors.put(field, err.getDefaultMessage());
        });

        // 2) Fetch and format the template
        String template = fetchTemplate("VALIDATION_FAILED", lang);
        String message  = formatMessage(template, lang);

        // 3) Build the ErrorResponse
        ErrorResponse error = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .errorCode("VALIDATION_FAILED")
                .fieldErrors(fieldErrors)
                .path(request.getRequestURI())
                .build();

        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(error);
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleUnsupportedMediaType(
            HttpMediaTypeNotSupportedException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        String template = fetchTemplate("UNSUPPORTED_MEDIA_TYPE", lang);
        String message  = formatMessage(template, lang, ex.getContentType());
        ErrorResponse error = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())
                .error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.getReasonPhrase())
                .message(message)
                .path(request.getRequestURI())
                .build();

        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(error);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgument(
            IllegalArgumentException ex,
            HttpServletRequest request
    ) {
        String lang     = resolveLanguage(request);
        String template = fetchTemplate("BAD_REQUEST", lang);
        String message  = formatMessage(template, lang, ex.getMessage());
        ErrorResponse error = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .path(request.getRequestURI())
                .build();

        return ResponseEntity.badRequest().body(error);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleNotFoundOrServerError(
            RuntimeException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        HttpStatus status;
        String code;

        // treat “not found” in message as 404
        if (ex.getMessage() != null && ex.getMessage().toLowerCase().contains("not found")) {
            status = HttpStatus.NOT_FOUND;
            code   = "RESOURCE_NOT_FOUND";
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            code   = "INTERNAL_SERVER_ERROR";
        }

        String template = fetchTemplate(code, lang);
        String message  = formatMessage(template, lang, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(status.value())
                .error(status.getReasonPhrase())
                .message(message)
                .path(request.getRequestURI())
                .build();

        return ResponseEntity.status(status).body(error);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleAllUncaught(
            Exception ex,
            HttpServletRequest request
    ) {
        log.error("Unexpected exception in Support Service", ex);

        String lang    = resolveLanguage(request);
        String template = fetchTemplate("INTERNAL_SERVER_ERROR", lang);
        String message  = formatMessage(template, lang);

        ErrorResponse error = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .error(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
                .message(message)
                .path(request.getRequestURI())
                .build();

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }

    // ─── Helpers ───────────────────────────────────────────────────────────────

    private String resolveLanguage(HttpServletRequest request) {
        return Optional.ofNullable(request.getHeader("Accept-Language"))
                .map(h -> h.split(",")[0].toUpperCase(Locale.ROOT))
                .orElse("EN");
    }

    private String fetchTemplate(String exceptionCode, String lang) {
        try {
            ExceptionMessageResponse resp = exceptionClient.getExceptionMessage(exceptionCode, lang);
            return Optional.ofNullable(resp).map(ExceptionMessageResponse::getMessage).orElse("No template");
        } catch (Exception e) {
            log.warn("Failed to fetch template for {}/{}: {}", exceptionCode, lang, e.getMessage());
            return "No template for '" + exceptionCode + "'";
        }
    }

    private String formatMessage(String template, String lang, Object... args) {
        if (template.contains("%")) {
            try {
                return String.format(template, args);
            } catch (IllegalFormatException ife) {
                log.error("Bad format '{}', args={}", template, Arrays.toString(args), ife);
                return template;
            }
        }
        // append first arg if no placeholder
        return args.length > 0 ? template + " " + args[0] : template;
    }
}
