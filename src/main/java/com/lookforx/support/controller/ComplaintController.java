package com.lookforx.support.controller;

import com.lookforx.support.dto.request.CreateComplaintTicketRequest;
import com.lookforx.support.dto.response.TicketResponse;
import com.lookforx.support.service.TicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * REST controller for handling complaint tickets.
 */
@RestController
@RequestMapping("/api/support/complaints")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Complaints", description = "API for managing complaint tickets")
public class ComplaintController {

    private final TicketService ticketService;

    /**
     * Create a new complaint ticket.
     */
    @Operation(
        summary = "Create complaint ticket",
        description = "Creates a new complaint ticket against a user, request, bid, or service"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Complaint ticket created successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @PostMapping
    public ResponseEntity<TicketResponse> createComplaintTicket(
            @Parameter(description = "Complaint ticket details", required = true)
            @Valid @RequestBody CreateComplaintTicketRequest request,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Creating complaint ticket for targetType: {}, targetId: {}", 
                request.getTargetType(), request.getTargetId());
        
        try {
            TicketResponse response = ticketService.createComplaintTicket(request);
            log.info("Complaint ticket created successfully with ID: {}", response.getTicketId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            log.error("Error creating complaint ticket: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get complaint ticket by ticket ID.
     */
    @Operation(
        summary = "Get complaint ticket by ID",
        description = "Retrieves a complaint ticket by its unique identifier"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Complaint ticket found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Complaint ticket not found",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/{ticketId}")
    public ResponseEntity<TicketResponse> getComplaintTicketById(
            @Parameter(description = "Unique identifier of the ticket", required = true)
            @PathVariable UUID ticketId,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching complaint ticket with ID: {}", ticketId);
        
        try {
            TicketResponse response = ticketService.getTicketById(ticketId);
            log.info("Complaint ticket found with ID: {}", ticketId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Complaint ticket not found with ID: {}", ticketId);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching complaint ticket: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get complaint ticket by bid ID.
     */
    @Operation(
        summary = "Get complaint ticket by bid ID",
        description = "Retrieves a complaint ticket by the related bid ID"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Complaint ticket found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Complaint ticket not found",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/by-bid/{bidId}")
    public ResponseEntity<TicketResponse> getComplaintTicketByBidId(
            @Parameter(description = "ID of the related bid", required = true, example = "BID-67890")
            @PathVariable String bidId,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching complaint ticket for bidId: {}", bidId);
        
        try {
            TicketResponse response = ticketService.getTicketByBidId(bidId);
            log.info("Complaint ticket found for bidId: {}", bidId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Complaint ticket not found for bidId: {}", bidId);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching complaint ticket: {}", e.getMessage(), e);
            throw e;
        }
    }
}
