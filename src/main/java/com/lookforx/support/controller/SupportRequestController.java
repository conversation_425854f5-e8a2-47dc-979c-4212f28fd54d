package com.lookforx.support.controller;

import com.lookforx.support.dto.request.CreateRequestTicketRequest;
import com.lookforx.support.dto.response.TicketResponse;
import com.lookforx.support.service.TicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * REST controller for handling support request tickets.
 */
@RestController
@RequestMapping("/api/support/requests")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Support Requests", description = "API for managing support request tickets")
public class SupportRequestController {

    private final TicketService ticketService;

    /**
     * Create a new support request ticket.
     */
    @Operation(
        summary = "Create support request ticket",
        description = "Creates a new support ticket for a service request or inquiry"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Support request ticket created successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @PostMapping
    public ResponseEntity<TicketResponse> createRequestTicket(
            @Parameter(description = "Support request ticket details", required = true)
            @Valid @RequestBody CreateRequestTicketRequest request,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Creating support request ticket for requestId: {}", request.getRequestId());
        
        try {
            TicketResponse response = ticketService.createRequestTicket(request);
            log.info("Support request ticket created successfully with ID: {}", response.getTicketId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            log.error("Error creating support request ticket: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get support request ticket by ticket ID.
     */
    @Operation(
        summary = "Get support request ticket by ID",
        description = "Retrieves a support request ticket by its unique identifier"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Support request ticket found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Support request ticket not found",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/{ticketId}")
    public ResponseEntity<TicketResponse> getRequestTicketById(
            @Parameter(description = "Unique identifier of the ticket", required = true)
            @PathVariable UUID ticketId,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching support request ticket with ID: {}", ticketId);
        
        try {
            TicketResponse response = ticketService.getTicketById(ticketId);
            log.info("Support request ticket found with ID: {}", ticketId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Support request ticket not found with ID: {}", ticketId);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching support request ticket: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get support request ticket by request ID.
     */
    @Operation(
        summary = "Get support request ticket by request ID",
        description = "Retrieves a support request ticket by the related request ID"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Support request ticket found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TicketResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Support request ticket not found",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/by-request/{requestId}")
    public ResponseEntity<TicketResponse> getRequestTicketByRequestId(
            @Parameter(description = "ID of the related request", required = true, example = "REQ-12345")
            @PathVariable String requestId,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching support request ticket for requestId: {}", requestId);
        
        try {
            TicketResponse response = ticketService.getTicketByRequestId(requestId);
            log.info("Support request ticket found for requestId: {}", requestId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Support request ticket not found for requestId: {}", requestId);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching support request ticket: {}", e.getMessage(), e);
            throw e;
        }
    }
}
