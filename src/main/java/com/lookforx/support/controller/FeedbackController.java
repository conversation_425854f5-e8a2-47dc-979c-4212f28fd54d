package com.lookforx.support.controller;

import com.lookforx.support.domain.enums.FeedbackCategory;
import com.lookforx.support.dto.request.CreateFeedbackRequest;
import com.lookforx.support.dto.response.FeedbackResponse;
import com.lookforx.support.service.FeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST controller for handling user feedback.
 */
@RestController
@RequestMapping("/api/support/feedback")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Feedback", description = "API for managing user feedback and suggestions")
public class FeedbackController {

    private final FeedbackService feedbackService;

    /**
     * Submit new feedback.
     */
    @Operation(
        summary = "Submit feedback",
        description = "Submits new user feedback (issue or suggestion)"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Feedback submitted successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FeedbackResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @PostMapping
    public ResponseEntity<FeedbackResponse> submitFeedback(
            @Parameter(description = "Feedback details", required = true)
            @Valid @RequestBody CreateFeedbackRequest request,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Submitting feedback with category: {}", request.getCategory());
        
        try {
            FeedbackResponse response = feedbackService.createFeedback(request);
            log.info("Feedback submitted successfully with ID: {}", response.getFeedbackId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            log.error("Error submitting feedback: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get feedback by ID.
     */
    @Operation(
        summary = "Get feedback by ID",
        description = "Retrieves feedback by its unique identifier"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Feedback found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FeedbackResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Feedback not found",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/{feedbackId}")
    public ResponseEntity<FeedbackResponse> getFeedbackById(
            @Parameter(description = "Unique identifier of the feedback", required = true)
            @PathVariable UUID feedbackId,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching feedback with ID: {}", feedbackId);
        
        try {
            FeedbackResponse response = feedbackService.getFeedbackById(feedbackId);
            log.info("Feedback found with ID: {}", feedbackId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Feedback not found with ID: {}", feedbackId);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching feedback: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get feedback with filtering and pagination.
     */
    @Operation(
        summary = "Get feedback with filters",
        description = "Retrieves feedback with optional filtering by category and pagination"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Feedback list retrieved successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping
    public ResponseEntity<Page<FeedbackResponse>> getFeedback(
            @Parameter(description = "Filter by feedback category", example = "ISSUE")
            @RequestParam(required = false) FeedbackCategory category,
            @Parameter(description = "Filter by feedback status", example = "SUBMITTED")
            @RequestParam(required = false) String status,
            @Parameter(description = "Filter by user ID")
            @RequestParam(required = false) UUID userId,
            @Parameter(description = "Filter by anonymous feedback", example = "false")
            @RequestParam(required = false) Boolean isAnonymous,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "20")
            @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field", example = "submittedAt")
            @RequestParam(defaultValue = "submittedAt") String sortBy,
            @Parameter(description = "Sort direction", example = "desc")
            @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching feedback with filters - category: {}, status: {}, page: {}, size: {}", 
                category, status, page, size);
        
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<FeedbackResponse> response = feedbackService.getFeedbackWithFilters(
                    category, status, userId, isAnonymous, pageable);
            
            log.info("Retrieved {} feedback items", response.getTotalElements());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching feedback: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Get feedback by category.
     */
    @Operation(
        summary = "Get feedback by category",
        description = "Retrieves all feedback for a specific category"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Feedback list retrieved successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/category/{category}")
    public ResponseEntity<List<FeedbackResponse>> getFeedbackByCategory(
            @Parameter(description = "Feedback category", required = true, example = "ISSUE")
            @PathVariable FeedbackCategory category,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Fetching feedback for category: {}", category);
        
        try {
            List<FeedbackResponse> response = feedbackService.getFeedbackByCategory(category);
            log.info("Retrieved {} feedback items for category: {}", response.size(), category);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching feedback by category: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Search feedback by message content.
     */
    @Operation(
        summary = "Search feedback",
        description = "Searches feedback by message content"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Search results retrieved successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/search")
    public ResponseEntity<List<FeedbackResponse>> searchFeedback(
            @Parameter(description = "Search term", required = true, example = "search functionality")
            @RequestParam String q,
            @Parameter(description = "Language code for internationalization", example = "en")
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String languageCode) {
        
        log.info("Searching feedback with term: {}", q);
        
        try {
            List<FeedbackResponse> response = feedbackService.searchFeedback(q);
            log.info("Found {} feedback items for search term: {}", response.size(), q);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error searching feedback: {}", e.getMessage(), e);
            throw e;
        }
    }
}
