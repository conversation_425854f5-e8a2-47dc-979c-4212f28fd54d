package com.lookforx.support.event;

import com.lookforx.support.domain.enums.TicketStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain event published when a support ticket status changes.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TicketStatusChangedEvent {

    private UUID ticketId;
    private TicketStatus oldStatus;
    private TicketStatus newStatus;
    @Builder.Default
    private LocalDateTime occurredAt = LocalDateTime.now();
    @Builder.Default
    private String eventType = "TICKET_STATUS_CHANGED";
}
