package com.lookforx.support.event;

import com.lookforx.support.domain.enums.FeedbackCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain event published when new feedback is submitted.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeedbackSubmittedEvent {

    private UUID feedbackId;
    private FeedbackCategory category;
    private UUID userId; // null for anonymous feedback
    private Boolean isAnonymous;
    @Builder.Default
    private LocalDateTime occurredAt = LocalDateTime.now();
    @Builder.Default
    private String eventType = "FEEDBACK_SUBMITTED";
}
