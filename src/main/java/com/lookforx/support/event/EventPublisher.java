package com.lookforx.support.event;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * Event publisher that listens to domain events and publishes them to Kafka.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EventPublisher {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    private static final String TICKET_TOPIC = "support.ticket.events";
    private static final String FEEDBACK_TOPIC = "support.feedback.events";

    /**
     * Handle ticket created events.
     */
    @EventListener
    public void handleTicketCreatedEvent(TicketCreatedEvent event) {
        log.info("Publishing ticket created event for ticket: {}", event.getTicketId());
        
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getTicketId().toString();
            
            CompletableFuture<SendResult<String, String>> future = 
                    kafkaTemplate.send(TICKET_TOPIC, key, eventJson);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Successfully published ticket created event for ticket: {} to topic: {} with offset: {}",
                            event.getTicketId(), TICKET_TOPIC, result.getRecordMetadata().offset());
                } else {
                    log.error("Failed to publish ticket created event for ticket: {} to topic: {}",
                            event.getTicketId(), TICKET_TOPIC, ex);
                }
            });
            
        } catch (JsonProcessingException e) {
            log.error("Error serializing ticket created event for ticket: {}", event.getTicketId(), e);
        }
    }

    /**
     * Handle ticket status changed events.
     */
    @EventListener
    public void handleTicketStatusChangedEvent(TicketStatusChangedEvent event) {
        log.info("Publishing ticket status changed event for ticket: {} from {} to {}", 
                event.getTicketId(), event.getOldStatus(), event.getNewStatus());
        
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getTicketId().toString();
            
            CompletableFuture<SendResult<String, String>> future = 
                    kafkaTemplate.send(TICKET_TOPIC, key, eventJson);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Successfully published ticket status changed event for ticket: {} to topic: {} with offset: {}",
                            event.getTicketId(), TICKET_TOPIC, result.getRecordMetadata().offset());
                } else {
                    log.error("Failed to publish ticket status changed event for ticket: {} to topic: {}",
                            event.getTicketId(), TICKET_TOPIC, ex);
                }
            });
            
        } catch (JsonProcessingException e) {
            log.error("Error serializing ticket status changed event for ticket: {}", event.getTicketId(), e);
        }
    }

    /**
     * Handle feedback submitted events.
     */
    @EventListener
    public void handleFeedbackSubmittedEvent(FeedbackSubmittedEvent event) {
        log.info("Publishing feedback submitted event for feedback: {}", event.getFeedbackId());
        
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getFeedbackId().toString();
            
            CompletableFuture<SendResult<String, String>> future = 
                    kafkaTemplate.send(FEEDBACK_TOPIC, key, eventJson);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Successfully published feedback submitted event for feedback: {} to topic: {} with offset: {}",
                            event.getFeedbackId(), FEEDBACK_TOPIC, result.getRecordMetadata().offset());
                } else {
                    log.error("Failed to publish feedback submitted event for feedback: {} to topic: {}",
                            event.getFeedbackId(), FEEDBACK_TOPIC, ex);
                }
            });
            
        } catch (JsonProcessingException e) {
            log.error("Error serializing feedback submitted event for feedback: {}", event.getFeedbackId(), e);
        }
    }
}
