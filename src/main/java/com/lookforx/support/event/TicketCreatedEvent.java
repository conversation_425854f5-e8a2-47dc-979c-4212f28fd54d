package com.lookforx.support.event;

import com.lookforx.support.domain.enums.TicketType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain event published when a new support ticket is created.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TicketCreatedEvent {

    private UUID ticketId;
    private TicketType ticketType;
    private String relatedEntityId; // requestId for REQUEST tickets, targetId for COMPLAINT tickets
    private UUID userId;
    @Builder.Default
    private LocalDateTime occurredAt = LocalDateTime.now();
    @Builder.Default
    private String eventType = "TICKET_CREATED";
}
