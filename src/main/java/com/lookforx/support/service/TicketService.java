package com.lookforx.support.service;

import com.lookforx.support.domain.entity.Ticket;
import com.lookforx.support.domain.enums.TicketStatus;
import com.lookforx.support.domain.enums.TicketType;
import com.lookforx.support.dto.request.CreateComplaintTicketRequest;
import com.lookforx.support.dto.request.CreateRequestTicketRequest;
import com.lookforx.support.dto.response.TicketResponse;
import com.lookforx.support.event.TicketCreatedEvent;
import com.lookforx.support.event.TicketStatusChangedEvent;
import com.lookforx.support.repository.TicketRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for managing support tickets.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TicketService {

    private final TicketRepository ticketRepository;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * Create a new REQUEST type ticket.
     */
    @CacheEvict(value = "tickets", allEntries = true)
    public TicketResponse createRequestTicket(CreateRequestTicketRequest request) {
        log.info("Creating request ticket for requestId: {}", request.getRequestId());

        Ticket ticket = Ticket.builder()
                .requestId(request.getRequestId())
                .type(TicketType.REQUEST)
                .status(TicketStatus.OPEN)
                .title(request.getTitle())
                .description(request.getDescription())
                .userId(request.getUserId())
                .priority(request.getPriority() != null ? request.getPriority() : "MEDIUM")
                .build();

        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish domain event
        eventPublisher.publishEvent(
                TicketCreatedEvent.builder()
                        .ticketId(savedTicket.getTicketId())
                        .ticketType(savedTicket.getType())
                        .relatedEntityId(savedTicket.getRequestId())
                        .userId(savedTicket.getUserId())
                        .build()
        );

        log.info("Request ticket created with ID: {}", savedTicket.getTicketId());
        return mapToResponse(savedTicket);
    }

    /**
     * Create a new COMPLAINT type ticket.
     */
    @CacheEvict(value = "tickets", allEntries = true)
    public TicketResponse createComplaintTicket(CreateComplaintTicketRequest request) {
        log.info("Creating complaint ticket for targetType: {}, targetId: {}", 
                request.getTargetType(), request.getTargetId());

        Ticket ticket = Ticket.builder()
                .bidId(request.getBidId())
                .type(TicketType.COMPLAINT)
                .status(TicketStatus.OPEN)
                .title(request.getTitle())
                .description(request.getDescription())
                .userId(request.getUserId())
                .priority(request.getPriority() != null ? request.getPriority() : "MEDIUM")
                .targetType(request.getTargetType())
                .targetId(request.getTargetId())
                .build();

        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish domain event
        eventPublisher.publishEvent(
                TicketCreatedEvent.builder()
                        .ticketId(savedTicket.getTicketId())
                        .ticketType(savedTicket.getType())
                        .relatedEntityId(savedTicket.getTargetId())
                        .userId(savedTicket.getUserId())
                        .build()
        );

        log.info("Complaint ticket created with ID: {}", savedTicket.getTicketId());
        return mapToResponse(savedTicket);
    }

    /**
     * Get ticket by ID.
     */
    @Cacheable(value = "tickets", key = "#ticketId")
    @Transactional(readOnly = true)
    public TicketResponse getTicketById(UUID ticketId) {
        log.debug("Fetching ticket with ID: {}", ticketId);
        
        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new RuntimeException("Ticket not found with ID: " + ticketId));
        
        return mapToResponse(ticket);
    }

    /**
     * Get ticket by request ID.
     */
    @Cacheable(value = "tickets", key = "'request:' + #requestId")
    @Transactional(readOnly = true)
    public TicketResponse getTicketByRequestId(String requestId) {
        log.debug("Fetching ticket for requestId: {}", requestId);
        
        Ticket ticket = ticketRepository.findByRequestId(requestId)
                .orElseThrow(() -> new RuntimeException("Ticket not found for requestId: " + requestId));
        
        return mapToResponse(ticket);
    }

    /**
     * Get ticket by bid ID.
     */
    @Cacheable(value = "tickets", key = "'bid:' + #bidId")
    @Transactional(readOnly = true)
    public TicketResponse getTicketByBidId(String bidId) {
        log.debug("Fetching ticket for bidId: {}", bidId);
        
        Ticket ticket = ticketRepository.findByBidId(bidId)
                .orElseThrow(() -> new RuntimeException("Ticket not found for bidId: " + bidId));
        
        return mapToResponse(ticket);
    }

    /**
     * Update ticket status.
     */
    @CacheEvict(value = "tickets", allEntries = true)
    public TicketResponse updateTicketStatus(UUID ticketId, TicketStatus newStatus, String resolutionNotes) {
        log.info("Updating ticket {} status to {}", ticketId, newStatus);
        
        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new RuntimeException("Ticket not found with ID: " + ticketId));
        
        TicketStatus oldStatus = ticket.getStatus();
        ticket.setStatus(newStatus);
        
        if (newStatus == TicketStatus.RESOLVED) {
            ticket.setResolvedAt(LocalDateTime.now());
            ticket.setResolutionNotes(resolutionNotes);
        }
        
        Ticket updatedTicket = ticketRepository.save(ticket);
        
        // Publish status change event
        eventPublisher.publishEvent(
                TicketStatusChangedEvent.builder()
                        .ticketId(ticketId)
                        .oldStatus(oldStatus)
                        .newStatus(newStatus)
                        .build()
        );
        
        log.info("Ticket {} status updated from {} to {}", ticketId, oldStatus, newStatus);
        return mapToResponse(updatedTicket);
    }

    /**
     * Get tickets with filtering and pagination.
     */
    @Transactional(readOnly = true)
    public Page<TicketResponse> getTicketsWithFilters(TicketType type, TicketStatus status, 
                                                     UUID userId, UUID assignedTo, Pageable pageable) {
        log.debug("Fetching tickets with filters - type: {}, status: {}, userId: {}, assignedTo: {}", 
                type, status, userId, assignedTo);
        
        Page<Ticket> tickets = ticketRepository.findTicketsWithFilters(type, status, userId, assignedTo, pageable);
        return tickets.map(this::mapToResponse);
    }

    /**
     * Search tickets by title or description.
     */
    @Transactional(readOnly = true)
    public List<TicketResponse> searchTickets(String searchTerm) {
        log.debug("Searching tickets with term: {}", searchTerm);
        
        List<Ticket> tickets = ticketRepository.searchTickets(searchTerm);
        return tickets.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Map Ticket entity to TicketResponse DTO.
     */
    private TicketResponse mapToResponse(Ticket ticket) {
        return TicketResponse.builder()
                .ticketId(ticket.getTicketId())
                .requestId(ticket.getRequestId())
                .bidId(ticket.getBidId())
                .type(ticket.getType())
                .status(ticket.getStatus())
                .title(ticket.getTitle())
                .description(ticket.getDescription())
                .userId(ticket.getUserId())
                .assignedTo(ticket.getAssignedTo())
                .priority(ticket.getPriority())
                .targetType(ticket.getTargetType())
                .targetId(ticket.getTargetId())
                .createdAt(ticket.getCreatedAt())
                .updatedAt(ticket.getUpdatedAt())
                .resolvedAt(ticket.getResolvedAt())
                .resolutionNotes(ticket.getResolutionNotes())
                .build();
    }
}
