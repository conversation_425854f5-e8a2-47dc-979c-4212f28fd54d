package com.lookforx.support.service;

import com.lookforx.support.domain.entity.Feedback;
import com.lookforx.support.domain.enums.FeedbackCategory;
import com.lookforx.support.dto.request.CreateFeedbackRequest;
import com.lookforx.support.dto.response.FeedbackResponse;
import com.lookforx.support.event.FeedbackSubmittedEvent;
import com.lookforx.support.repository.FeedbackRepository;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for managing user feedback.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FeedbackService {

    private final FeedbackRepository feedbackRepository;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * Create new feedback.
     */
    @CacheEvict(value = "feedback", allEntries = true)
    public FeedbackResponse createFeedback(CreateFeedbackRequest request) {
        log.info("Creating feedback with category: {}, anonymous: {}", 
                request.getCategory(), request.getIsAnonymous());

        boolean isAnonymous = request.getIsAnonymous() != null ? 
                request.getIsAnonymous() : (request.getUserId() == null);

        Feedback feedback = Feedback.builder()
                .category(request.getCategory())
                .message(request.getMessage())
                .userId(request.getUserId())
                .userEmail(request.getUserEmail())
                .isAnonymous(isAnonymous)
                .status("SUBMITTED")
                .build();

        Feedback savedFeedback = feedbackRepository.save(feedback);
        
        // Publish domain event
        eventPublisher.publishEvent(
                FeedbackSubmittedEvent.builder()
                        .feedbackId(savedFeedback.getFeedbackId())
                        .category(savedFeedback.getCategory())
                        .userId(savedFeedback.getUserId())
                        .isAnonymous(savedFeedback.getIsAnonymous())
                        .build()
        );

        log.info("Feedback created with ID: {}", savedFeedback.getFeedbackId());
        return mapToResponse(savedFeedback);
    }

    /**
     * Get feedback by ID.
     */
    @Cacheable(value = "feedback", key = "#feedbackId")
    @Transactional(readOnly = true)
    public FeedbackResponse getFeedbackById(UUID feedbackId) {
        log.debug("Fetching feedback with ID: {}", feedbackId);
        
        Feedback feedback = feedbackRepository.findById(feedbackId)
                .orElseThrow(() -> new RuntimeException("Feedback not found with ID: " + feedbackId));
        
        return mapToResponse(feedback);
    }

    /**
     * Get feedback with filtering and pagination.
     */
    @Transactional(readOnly = true)
    public Page<FeedbackResponse> getFeedbackWithFilters(FeedbackCategory category, String status, 
                                                        UUID userId, Boolean isAnonymous, Pageable pageable) {
        log.debug("Fetching feedback with filters - category: {}, status: {}, userId: {}, anonymous: {}", 
                category, status, userId, isAnonymous);
        
        Page<Feedback> feedback = feedbackRepository.findFeedbackWithFilters(category, status, userId, isAnonymous, pageable);
        return feedback.map(this::mapToResponse);
    }

    /**
     * Get feedback by category.
     */
    @Cacheable(value = "feedback", key = "'category:' + #category")
    @Transactional(readOnly = true)
    public List<FeedbackResponse> getFeedbackByCategory(FeedbackCategory category) {
        log.debug("Fetching feedback for category: {}", category);
        
        List<Feedback> feedback = feedbackRepository.findByCategory(category);
        return feedback.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get feedback by user ID.
     */
    @Cacheable(value = "feedback", key = "'user:' + #userId")
    @Transactional(readOnly = true)
    public List<FeedbackResponse> getFeedbackByUserId(UUID userId) {
        log.debug("Fetching feedback for userId: {}", userId);
        
        List<Feedback> feedback = feedbackRepository.findByUserId(userId);
        return feedback.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Search feedback by message content.
     */
    @Transactional(readOnly = true)
    public List<FeedbackResponse> searchFeedback(String searchTerm) {
        log.debug("Searching feedback with term: {}", searchTerm);
        
        List<Feedback> feedback = feedbackRepository.searchFeedback(searchTerm);
        return feedback.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Update feedback status (admin operation).
     */
    @CacheEvict(value = "feedback", allEntries = true)
    public FeedbackResponse updateFeedbackStatus(UUID feedbackId, String newStatus, 
                                                String adminNotes, UUID reviewedBy) {
        log.info("Updating feedback {} status to {}", feedbackId, newStatus);
        
        Feedback feedback = feedbackRepository.findById(feedbackId)
                .orElseThrow(() -> new RuntimeException("Feedback not found with ID: " + feedbackId));
        
        feedback.setStatus(newStatus);
        feedback.setAdminNotes(adminNotes);
        feedback.setReviewedAt(LocalDateTime.now());
        feedback.setReviewedBy(reviewedBy);
        
        Feedback updatedFeedback = feedbackRepository.save(feedback);
        
        log.info("Feedback {} status updated to {}", feedbackId, newStatus);
        return mapToResponse(updatedFeedback);
    }

    /**
     * Get unreviewed feedback.
     */
    @Transactional(readOnly = true)
    public List<FeedbackResponse> getUnreviewedFeedback() {
        log.debug("Fetching unreviewed feedback");
        
        List<Feedback> feedback = feedbackRepository.findUnreviewedFeedback();
        return feedback.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get feedback statistics.
     */
    @Transactional(readOnly = true)
    public FeedbackStatistics getFeedbackStatistics() {
        log.debug("Calculating feedback statistics");
        
        long totalFeedback = feedbackRepository.count();
        long issueCount = feedbackRepository.countByCategory(FeedbackCategory.ISSUE);
        long suggestionCount = feedbackRepository.countByCategory(FeedbackCategory.SUGGESTION);
        long submittedCount = feedbackRepository.countByStatus("SUBMITTED");
        long reviewedCount = feedbackRepository.countByStatus("REVIEWED");
        long addressedCount = feedbackRepository.countByStatus("ADDRESSED");
        
        return FeedbackStatistics.builder()
                .totalFeedback(totalFeedback)
                .issueCount(issueCount)
                .suggestionCount(suggestionCount)
                .submittedCount(submittedCount)
                .reviewedCount(reviewedCount)
                .addressedCount(addressedCount)
                .build();
    }

    /**
     * Map Feedback entity to FeedbackResponse DTO.
     */
    private FeedbackResponse mapToResponse(Feedback feedback) {
        return FeedbackResponse.builder()
                .feedbackId(feedback.getFeedbackId())
                .category(feedback.getCategory())
                .message(feedback.getMessage())
                .userId(feedback.getUserId())
                .userEmail(feedback.getUserEmail())
                .isAnonymous(feedback.getIsAnonymous())
                .submittedAt(feedback.getSubmittedAt())
                .status(feedback.getStatus())
                .adminNotes(feedback.getAdminNotes())
                .reviewedAt(feedback.getReviewedAt())
                .reviewedBy(feedback.getReviewedBy())
                .build();
    }

    /**
     * Inner class for feedback statistics.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeedbackStatistics {
        private long totalFeedback;
        private long issueCount;
        private long suggestionCount;
        private long submittedCount;
        private long reviewedCount;
        private long addressedCount;
    }
}
