package com.lookforx.support.repository;

import com.lookforx.support.domain.entity.Feedback;
import com.lookforx.support.domain.enums.FeedbackCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for Feedback entity operations.
 */
@Repository
public interface FeedbackRepository extends JpaRepository<Feedback, UUID> {

    /**
     * Find feedback by category.
     */
    List<Feedback> findByCategory(FeedbackCategory category);

    /**
     * Find feedback by user ID.
     */
    List<Feedback> findByUserId(UUID userId);

    /**
     * Find anonymous feedback.
     */
    List<Feedback> findByIsAnonymousTrue();

    /**
     * Find feedback by status.
     */
    List<Feedback> findByStatus(String status);

    /**
     * Find feedback submitted within a date range.
     */
    @Query("SELECT f FROM Feedback f WHERE f.submittedAt BETWEEN :startDate AND :endDate")
    List<Feedback> findBySubmittedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    /**
     * Find feedback by category and status.
     */
    List<Feedback> findByCategoryAndStatus(FeedbackCategory category, String status);

    /**
     * Count feedback by category.
     */
    long countByCategory(FeedbackCategory category);

    /**
     * Count feedback by status.
     */
    long countByStatus(String status);

    /**
     * Find feedback with pagination and filtering.
     */
    @Query("SELECT f FROM Feedback f WHERE " +
           "(:category IS NULL OR f.category = :category) AND " +
           "(:status IS NULL OR f.status = :status) AND " +
           "(:userId IS NULL OR f.userId = :userId) AND " +
           "(:isAnonymous IS NULL OR f.isAnonymous = :isAnonymous)")
    Page<Feedback> findFeedbackWithFilters(@Param("category") FeedbackCategory category,
                                          @Param("status") String status,
                                          @Param("userId") UUID userId,
                                          @Param("isAnonymous") Boolean isAnonymous,
                                          Pageable pageable);

    /**
     * Search feedback by message content.
     */
    @Query("SELECT f FROM Feedback f WHERE " +
           "LOWER(f.message) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Feedback> searchFeedback(@Param("searchTerm") String searchTerm);

    /**
     * Find unreviewed feedback.
     */
    @Query("SELECT f FROM Feedback f WHERE f.reviewedAt IS NULL")
    List<Feedback> findUnreviewedFeedback();

    /**
     * Find feedback reviewed by specific admin.
     */
    List<Feedback> findByReviewedBy(UUID reviewedBy);
}
