package com.lookforx.support.repository;

import com.lookforx.support.domain.entity.Ticket;
import com.lookforx.support.domain.enums.TicketStatus;
import com.lookforx.support.domain.enums.TicketType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Ticket entity operations.
 */
@Repository
public interface TicketRepository extends JpaRepository<Ticket, UUID> {

    /**
     * Find ticket by request ID.
     */
    Optional<Ticket> findByRequestId(String requestId);

    /**
     * Find ticket by bid ID.
     */
    Optional<Ticket> findByBidId(String bidId);

    /**
     * Find tickets by type.
     */
    List<Ticket> findByType(TicketType type);

    /**
     * Find tickets by status.
     */
    List<Ticket> findByStatus(TicketStatus status);

    /**
     * Find tickets by user ID.
     */
    List<Ticket> findByUserId(UUID userId);

    /**
     * Find tickets assigned to a specific user.
     */
    List<Ticket> findByAssignedTo(UUID assignedTo);

    /**
     * Find tickets by type and status.
     */
    List<Ticket> findByTypeAndStatus(TicketType type, TicketStatus status);

    /**
     * Find tickets created within a date range.
     */
    @Query("SELECT t FROM Ticket t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    List<Ticket> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Find tickets by target type and target ID (for complaints).
     */
    List<Ticket> findByTargetTypeAndTargetId(String targetType, String targetId);

    /**
     * Find open tickets older than specified date.
     */
    @Query("SELECT t FROM Ticket t WHERE t.status = 'OPEN' AND t.createdAt < :cutoffDate")
    List<Ticket> findOpenTicketsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Count tickets by status.
     */
    long countByStatus(TicketStatus status);

    /**
     * Count tickets by type.
     */
    long countByType(TicketType type);

    /**
     * Find tickets with pagination and filtering.
     */
    @Query("SELECT t FROM Ticket t WHERE " +
           "(:type IS NULL OR t.type = :type) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:userId IS NULL OR t.userId = :userId) AND " +
           "(:assignedTo IS NULL OR t.assignedTo = :assignedTo)")
    Page<Ticket> findTicketsWithFilters(@Param("type") TicketType type,
                                       @Param("status") TicketStatus status,
                                       @Param("userId") UUID userId,
                                       @Param("assignedTo") UUID assignedTo,
                                       Pageable pageable);

    /**
     * Search tickets by title or description.
     */
    @Query("SELECT t FROM Ticket t WHERE " +
           "LOWER(t.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(t.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Ticket> searchTickets(@Param("searchTerm") String searchTerm);
}
