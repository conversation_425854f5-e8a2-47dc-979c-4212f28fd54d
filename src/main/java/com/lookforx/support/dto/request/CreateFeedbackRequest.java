package com.lookforx.support.dto.request;

import com.lookforx.support.domain.enums.FeedbackCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Request DTO for creating user feedback.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Request to create user feedback")
public class CreateFeedbackRequest {

    @NotNull(message = "Category is required")
    @Schema(description = "Category of the feedback", example = "ISSUE")
    private FeedbackCategory category;

    @NotBlank(message = "Message is required")
    @Size(max = 5000, message = "Message must not exceed 5000 characters")
    @Schema(description = "Feedback message content", 
            example = "I think the search functionality could be improved by adding more filter options.")
    private String message;

    @Schema(description = "ID of the user providing feedback (optional for anonymous feedback)", 
            example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @Email(message = "Invalid email format")
    @Size(max = 255, message = "Email must not exceed 255 characters")
    @Schema(description = "Email address for anonymous feedback contact (optional)", 
            example = "<EMAIL>")
    private String userEmail;

    @Schema(description = "Whether this feedback is submitted anonymously", example = "false")
    private Boolean isAnonymous;
}
