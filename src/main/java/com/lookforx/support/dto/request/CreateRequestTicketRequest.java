package com.lookforx.support.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Request DTO for creating a REQUEST type support ticket.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Request to create a support request ticket")
public class CreateRequestTicketRequest {

    @NotBlank(message = "Request ID is required")
    @Size(max = 100, message = "Request ID must not exceed 100 characters")
    @Schema(description = "ID of the request this ticket is related to", example = "REQ-12345")
    private String requestId;

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    @Schema(description = "Title of the support ticket", example = "Issue with request processing")
    private String title;

    @NotBlank(message = "Description is required")
    @Size(max = 5000, message = "Description must not exceed 5000 characters")
    @Schema(description = "Detailed description of the issue or request", 
            example = "I am experiencing issues with my request processing. The status has not been updated for several days.")
    private String description;

    @Schema(description = "ID of the user creating the ticket", example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @Size(max = 20, message = "Priority must not exceed 20 characters")
    @Schema(description = "Priority level of the ticket", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "URGENT"})
    private String priority;
}
