package com.lookforx.support.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Request DTO for creating a COMPLAINT type support ticket.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Request to create a complaint ticket")
public class CreateComplaintTicketRequest {

    @Size(max = 100, message = "Bid ID must not exceed 100 characters")
    @Schema(description = "ID of the bid this complaint is related to (optional)", example = "BID-67890")
    private String bidId;

    @NotBlank(message = "Target type is required")
    @Size(max = 50, message = "Target type must not exceed 50 characters")
    @Schema(description = "Type of entity being complained about", 
            example = "USER", 
            allowableValues = {"USER", "REQUEST", "BID", "SERVICE"})
    private String targetType;

    @NotBlank(message = "Target ID is required")
    @Size(max = 100, message = "Target ID must not exceed 100 characters")
    @Schema(description = "ID of the entity being complained about", example = "USER-12345")
    private String targetId;

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    @Schema(description = "Title of the complaint", example = "Inappropriate behavior by user")
    private String title;

    @NotBlank(message = "Description is required")
    @Size(max = 5000, message = "Description must not exceed 5000 characters")
    @Schema(description = "Detailed description of the complaint", 
            example = "The user has been sending inappropriate messages and not following platform guidelines.")
    private String description;

    @Schema(description = "ID of the user filing the complaint", example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @Size(max = 20, message = "Priority must not exceed 20 characters")
    @Schema(description = "Priority level of the complaint", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "URGENT"})
    private String priority;
}
