package com.lookforx.support.dto.response;

import com.lookforx.support.domain.enums.TicketStatus;
import com.lookforx.support.domain.enums.TicketType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO for support ticket information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Support ticket information")
public class TicketResponse {

    @Schema(description = "Unique identifier of the ticket", example = "123e4567-e89b-12d3-a456-************")
    private UUID ticketId;

    @Schema(description = "ID of the related request (if applicable)", example = "REQ-12345")
    private String requestId;

    @Schema(description = "ID of the related bid (if applicable)", example = "BID-67890")
    private String bidId;

    @Schema(description = "Type of the ticket", example = "REQUEST")
    private TicketType type;

    @Schema(description = "Current status of the ticket", example = "OPEN")
    private TicketStatus status;

    @Schema(description = "Title of the ticket", example = "Issue with request processing")
    private String title;

    @Schema(description = "Detailed description of the ticket", 
            example = "I am experiencing issues with my request processing.")
    private String description;

    @Schema(description = "ID of the user who created the ticket", example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @Schema(description = "ID of the support agent assigned to the ticket", example = "456e7890-e89b-12d3-a456-************")
    private UUID assignedTo;

    @Schema(description = "Priority level of the ticket", example = "MEDIUM")
    private String priority;

    @Schema(description = "Type of entity being complained about (for complaints)", example = "USER")
    private String targetType;

    @Schema(description = "ID of the entity being complained about (for complaints)", example = "USER-12345")
    private String targetId;

    @Schema(description = "Timestamp when the ticket was created", example = "2024-01-15T10:30:00")
    private LocalDateTime createdAt;

    @Schema(description = "Timestamp when the ticket was last updated", example = "2024-01-15T14:45:00")
    private LocalDateTime updatedAt;

    @Schema(description = "Timestamp when the ticket was resolved (if applicable)", example = "2024-01-16T09:15:00")
    private LocalDateTime resolvedAt;

    @Schema(description = "Resolution notes (if ticket is resolved)", 
            example = "Issue was resolved by updating the request status.")
    private String resolutionNotes;
}
