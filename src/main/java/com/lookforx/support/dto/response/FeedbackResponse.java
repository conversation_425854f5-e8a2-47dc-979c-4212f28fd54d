package com.lookforx.support.dto.response;

import com.lookforx.support.domain.enums.FeedbackCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO for feedback information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "User feedback information")
public class FeedbackResponse {

    @Schema(description = "Unique identifier of the feedback", example = "123e4567-e89b-12d3-a456-************")
    private UUID feedbackId;

    @Schema(description = "Category of the feedback", example = "ISSUE")
    private FeedbackCategory category;

    @Schema(description = "Feedback message content", 
            example = "I think the search functionality could be improved.")
    private String message;

    @Schema(description = "ID of the user who provided the feedback (null for anonymous)", 
            example = "123e4567-e89b-12d3-a456-************")
    private UUID userId;

    @Schema(description = "Email address for anonymous feedback contact", example = "<EMAIL>")
    private String userEmail;

    @Schema(description = "Whether this feedback was submitted anonymously", example = "false")
    private Boolean isAnonymous;

    @Schema(description = "Timestamp when the feedback was submitted", example = "2024-01-15T10:30:00")
    private LocalDateTime submittedAt;

    @Schema(description = "Current status of the feedback", example = "SUBMITTED")
    private String status;

    @Schema(description = "Admin notes on the feedback", 
            example = "Feedback has been forwarded to the development team.")
    private String adminNotes;

    @Schema(description = "Timestamp when the feedback was reviewed", example = "2024-01-16T09:15:00")
    private LocalDateTime reviewedAt;

    @Schema(description = "ID of the admin who reviewed the feedback", example = "456e7890-e89b-12d3-a456-************")
    private UUID reviewedBy;
}
