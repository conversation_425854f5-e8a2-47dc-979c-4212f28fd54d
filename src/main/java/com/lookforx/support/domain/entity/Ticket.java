package com.lookforx.support.domain.entity;

import com.lookforx.support.domain.enums.TicketStatus;
import com.lookforx.support.domain.enums.TicketType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing a customer support ticket.
 * Can be either a REQUEST or COMPLAINT type.
 */
@Entity
@Table(name = "tickets", indexes = {
    @Index(name = "idx_ticket_request_id", columnList = "requestId"),
    @Index(name = "idx_ticket_bid_id", columnList = "bidId"),
    @Index(name = "idx_ticket_type", columnList = "type"),
    @Index(name = "idx_ticket_status", columnList = "status"),
    @Index(name = "idx_ticket_created_at", columnList = "createdAt")
})
@EntityListeners(AuditingEntityListener.class)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Ticket {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ticket_id", updatable = false, nullable = false)
    private UUID ticketId;

    @Column(name = "request_id", nullable = true, length = 100)
    private String requestId;

    @Column(name = "bid_id", nullable = true, length = 100)
    private String bidId;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private TicketType type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Builder.Default
    private TicketStatus status = TicketStatus.OPEN;

    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(name = "user_id", nullable = true)
    private UUID userId;

    @Column(name = "assigned_to", nullable = true)
    private UUID assignedTo;

    @Column(name = "priority", nullable = true, length = 20)
    @Builder.Default
    private String priority = "MEDIUM";

    @Column(name = "target_type", nullable = true, length = 50)
    private String targetType; // For complaints: USER, REQUEST, BID, etc.

    @Column(name = "target_id", nullable = true, length = 100)
    private String targetId; // ID of the target being complained about

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "resolved_at", nullable = true)
    private LocalDateTime resolvedAt;

    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;

    @PrePersist
    protected void onCreate() {
        if (ticketId == null) {
            ticketId = UUID.randomUUID();
        }
        if (status == null) {
            status = TicketStatus.OPEN;
        }
        if (priority == null) {
            priority = "MEDIUM";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        if (status == TicketStatus.RESOLVED && resolvedAt == null) {
            resolvedAt = LocalDateTime.now();
        }
    }
}
