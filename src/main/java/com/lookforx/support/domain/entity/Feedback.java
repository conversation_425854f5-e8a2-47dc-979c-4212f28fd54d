package com.lookforx.support.domain.entity;

import com.lookforx.support.domain.enums.FeedbackCategory;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing user feedback (issues or suggestions).
 */
@Entity
@Table(name = "feedback", indexes = {
    @Index(name = "idx_feedback_category", columnList = "category"),
    @Index(name = "idx_feedback_user_id", columnList = "userId"),
    @Index(name = "idx_feedback_submitted_at", columnList = "submittedAt")
})
@EntityListeners(AuditingEntityListener.class)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Feedback {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "feedback_id", updatable = false, nullable = false)
    private UUID feedbackId;

    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false, length = 20)
    private FeedbackCategory category;

    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    private String message;

    @Column(name = "user_id", nullable = true)
    private UUID userId; // Optional - can be anonymous

    @Column(name = "user_email", nullable = true, length = 255)
    private String userEmail; // For anonymous feedback contact

    @Column(name = "is_anonymous", nullable = false)
    @Builder.Default
    private Boolean isAnonymous = false;

    @CreatedDate
    @Column(name = "submitted_at", nullable = false, updatable = false)
    private LocalDateTime submittedAt;

    @Column(name = "status", nullable = false, length = 20)
    @Builder.Default
    private String status = "SUBMITTED"; // SUBMITTED, REVIEWED, ADDRESSED

    @Column(name = "admin_notes", columnDefinition = "TEXT")
    private String adminNotes;

    @Column(name = "reviewed_at", nullable = true)
    private LocalDateTime reviewedAt;

    @Column(name = "reviewed_by", nullable = true)
    private UUID reviewedBy;

    @PrePersist
    protected void onCreate() {
        if (feedbackId == null) {
            feedbackId = UUID.randomUUID();
        }
        if (status == null) {
            status = "SUBMITTED";
        }
        if (isAnonymous == null) {
            isAnonymous = userId == null;
        }
    }
}
