package com.lookforx.searchservice.service.impl;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.repository.SearchCategoryRepository;
import com.lookforx.searchservice.service.SearchCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.NoSuchElementException;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchCategoryServiceImpl implements SearchCategoryService {

    private final SearchCategoryRepository repository;
    private final ElasticsearchOperations elasticsearchOperations;

    @Override
    @Cacheable(value = "categorySearch", key = "#query + '_' + #mode + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<SearchCategoryDocument> search(String query, String mode, Pageable pageable) {
        return search(query, mode, null, pageable);
    }

    @Override
    @Cacheable(value = "categorySearchWithLanguage", key = "#query + '_' + #mode + '_' + #language + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<SearchCategoryDocument> search(String query, String mode, String language, Pageable pageable) {
        log.info("Searching categories with query: '{}', mode: {}, language: {}", query, mode, language);

        if (!StringUtils.hasText(query)) {
            log.warn("Empty search query provided");
            return Page.empty(pageable);
        }

        try {
            if (StringUtils.hasText(language)) {
                // Search in specific language
                return switch (mode.toUpperCase()) {
                    case "FUZZY" -> repository.searchFuzzyByLanguage(query, language, pageable);
                    case "PHRASE" -> repository.searchPhraseByLanguage(query, language, pageable);
                    case "SIMPLE", "FULL_TEXT" -> repository.searchSimpleByLanguage(query, language, pageable);
                    default -> repository.searchSimpleByLanguage(query, language, pageable);
                };
            } else {
                // Search in all languages
                return switch (mode.toUpperCase()) {
                    case "FUZZY" -> repository.searchFuzzy(query, pageable);
                    case "PHRASE" -> repository.searchPhrase(query, pageable);
                    case "SIMPLE", "FULL_TEXT" -> repository.searchSimple(query, pageable);
                    default -> repository.searchSimple(query, pageable);
                };
            }
        } catch (Exception e) {
            log.error("Error during search for query: '{}', mode: {}, language: {}", query, mode, language, e);
            return Page.empty(pageable);
        }
    }

    @Override
    @Cacheable(value = "categorySearchByType", key = "#query + '_' + #type + '_' + #mode + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<SearchCategoryDocument> searchByType(String query, String type, String mode, Pageable pageable) {
        // Search by type and query

        if (!StringUtils.hasText(query) || !StringUtils.hasText(type)) {
            return Page.empty(pageable);
        }

        try {
            return repository.searchByType(query, type.toUpperCase(), pageable);
        } catch (Exception e) {
            log.error("Error during search by type: '{}', query: '{}', mode: {}", type, query, mode, e);
            return Page.empty(pageable);
        }
    }

    @Override
    @Cacheable(value = "categorySearchByLevel", key = "#query + '_' + #level + '_' + #mode + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<SearchCategoryDocument> searchByLevel(String query, Integer level, String mode, Pageable pageable) {
        // Search by level and query

        if (!StringUtils.hasText(query) || level == null) {
            return Page.empty(pageable);
        }

        try {
            return repository.searchByLevel(query, level, pageable);
        } catch (Exception e) {
            log.error("Error during search by level: {}, query: '{}', mode: {}", level, query, mode, e);
            return Page.empty(pageable);
        }
    }

    @Override
    @Cacheable(value = "categoryAutocomplete", key = "#query + '_' + #pageable.pageSize")
    public Page<SearchCategoryDocument> autocomplete(String query, Pageable pageable) {
        // Autocomplete search

        if (!StringUtils.hasText(query) || query.length() < 2) {
            return Page.empty(pageable);
        }

        try {
            return repository.autocomplete(query, pageable);
        } catch (Exception e) {
            log.error("Error during autocomplete for query: '{}'", query, e);
            return Page.empty(pageable);
        }
    }

    @Override
    @Cacheable(value = "categoriesByType", key = "#type")
    public List<SearchCategoryDocument> getCategoriesByType(String type) {
        // Get categories by type

        if (!StringUtils.hasText(type)) {
            return List.of();
        }

        try {
            return repository.findByType(type.toUpperCase());
        } catch (Exception e) {
            log.error("Error getting categories by type: {}", type, e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "categoriesByLevel", key = "#level")
    public List<SearchCategoryDocument> getCategoriesByLevel(Integer level) {
        // Get categories by level

        if (level == null) {
            return List.of();
        }

        try {
            return repository.findByLevel(level);
        } catch (Exception e) {
            log.error("Error getting categories by level: {}", level, e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "categoriesByParentId", key = "#parentId")
    public List<SearchCategoryDocument> getCategoriesByParentId(Long parentId) {
        // Get categories by parent ID

        if (parentId == null) {
            return List.of();
        }

        try {
            return repository.findByParentId(parentId);
        } catch (Exception e) {
            log.error("Error getting categories by parent ID: {}", parentId, e);
            return List.of();
        }
    }

    @Override
    @CacheEvict(value = {"categorySearch", "categorySearchByType", "categorySearchByLevel",
                        "categoryAutocomplete", "categoriesByType", "categoriesByLevel",
                        "categoriesByParentId"}, allEntries = true)
    public void indexCategory(SearchCategoryDocument document) {
        // Index category

        try {
            // Set timestamps
            if (document.getCreatedAt() == null) {
                document.setCreatedAt(LocalDate.now());
            }
            document.setUpdatedAt(LocalDate.now());

            repository.save(document);
            log.info("Successfully indexed category with ID: {}", document.getId());
        } catch (Exception e) {
            log.error("Error indexing category with ID: {}", document.getId(), e);
            throw new RuntimeException("Failed to index category", e);
        }
    }

    @Override
    @CacheEvict(value = {"categorySearch", "categorySearchByType", "categorySearchByLevel",
                        "categoryAutocomplete", "categoriesByType", "categoriesByLevel",
                        "categoriesByParentId"}, allEntries = true)
    public void updateCategory(String id, SearchCategoryDocument document) {
        // Update category

        try {
            document.setId(id);
            document.setUpdatedAt(LocalDate.now());

            repository.save(document);
            log.info("Successfully updated category with ID: {}", id);
        } catch (Exception e) {
            log.error("Error updating category with ID: {}", id, e);
            throw new RuntimeException("Failed to update category", e);
        }
    }

    @Override
    @CacheEvict(value = {"categorySearch", "categorySearchByType", "categorySearchByLevel", 
                        "categoryAutocomplete", "categoriesByType", "categoriesByLevel", 
                        "categoriesByParentId"}, allEntries = true)
    public void deleteCategory(String id) {
        // Delete category
        
        try {
            if (!repository.existsById(id)) {
                throw new NoSuchElementException("Category not found with id: " + id);
            }
            
            repository.deleteById(id);
            log.info("Successfully deleted category with ID: {}", id);
        } catch (Exception e) {
            log.error("Error deleting category with ID: {}", id, e);
            throw new RuntimeException("Failed to delete category", e);
        }
    }

    @Override
    @CacheEvict(value = {"categorySearch", "categorySearchByType", "categorySearchByLevel", 
                        "categoryAutocomplete", "categoriesByType", "categoriesByLevel", 
                        "categoriesByParentId"}, allEntries = true)
    public void clearAllCategories() {
        // Clear all categories
        
        try {
            repository.deleteAll();
            log.info("Successfully cleared all categories from index");
        } catch (Exception e) {
            log.error("Error clearing all categories from index", e);
            throw new RuntimeException("Failed to clear all categories", e);
        }
    }

    @Override
    @CacheEvict(value = {"categorySearch", "categorySearchByType", "categorySearchByLevel",
                        "categoryAutocomplete", "categoriesByType", "categoriesByLevel",
                        "categoriesByParentId"}, allEntries = true)
    public void bulkIndexCategories(List<SearchCategoryDocument> documents) {
        // Bulk index categories

        try {
            LocalDate now = LocalDate.now();
            documents.forEach(doc -> {
                if (doc.getCreatedAt() == null) {
                    doc.setCreatedAt(now);
                }
                doc.setUpdatedAt(now);
            });

            repository.saveAll(documents);
            log.info("Successfully bulk indexed {} categories", documents.size());
        } catch (Exception e) {
            log.error("Error during bulk indexing of {} categories", documents.size(), e);
            throw new RuntimeException("Failed to bulk index categories", e);
        }
    }

    @Override
    public boolean isIndexHealthy() {
        try {
            IndexOperations indexOps = elasticsearchOperations.indexOps(SearchCategoryDocument.class);
            return indexOps.exists();
        } catch (Exception e) {
            log.error("Error checking index health", e);
            return false;
        }
    }

    @Override
    public void refreshIndex() {
        try {
            IndexOperations indexOps = elasticsearchOperations.indexOps(SearchCategoryDocument.class);
            indexOps.refresh();
            // Index refreshed
        } catch (Exception e) {
            log.error("Error refreshing index", e);
            throw new RuntimeException("Failed to refresh index", e);
        }
    }
}
