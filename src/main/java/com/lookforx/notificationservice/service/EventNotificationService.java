package com.lookforx.notificationservice.service;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.common.events.*;
import com.lookforx.notificationservice.domain.Notification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for handling domain events and creating notifications
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EventNotificationService {

    private final NotificationService notificationService;
    private final OptimizedBulkNotificationService optimizedBulkNotificationService;
    private final UserNotificationService userNotificationService;
    
    /**
     * Handle user registered event
     */
    public void handleUserRegisteredEvent(UserRegisteredEvent event) {
        log.info("Processing user registered event: userId={}, email={}", event.getUserId(), event.getEmail());
        
        // Welcome email
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("firstName", event.getFirstName());
        emailParams.put("lastName", event.getLastName());
        emailParams.put("verificationToken", event.getVerificationToken());
        
        Notification emailNotification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getEmail())
                .subject("Welcome to LookForX - Please verify your email")
                .content("Welcome to LookForX! Please verify your email address to complete your registration.")
                .templateName("user-welcome-email")
                .templateParameters(emailParams)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(emailNotification);

        // Welcome SMS (if phone number provided)
        if (event.getPhoneNumber() != null && !event.getPhoneNumber().isEmpty()) {
            Notification smsNotification = Notification.builder()
                    .eventId(event.getEventId() + "-sms")
                    .eventType(event.getEventType())
                    .notificationType(NotificationType.SMS)
                    .priority(NotificationPriority.NORMAL)
                    .userId(event.getUserId())
                    .recipient(event.getPhoneNumber())
                    .subject("Welcome to LookForX")
                    .content("Welcome to LookForX! Your account has been created successfully.")
                    .correlationId(event.getCorrelationId())
                    .serviceName(event.getServiceName())
                    .build();

            notificationService.sendNotification(smsNotification);
        }

        // Bell notification for welcome - handled by bulk notification system
    }
    
    /**
     * Handle user login event
     */
    public void handleUserLoginEvent(UserLoginEvent event) {
        log.info("Processing user login event: userId={}, successful={}", event.getUserId(), event.isSuccessful());
        
        if (!event.isSuccessful()) {
            // Failed login notification
            Map<String, String> params = new HashMap<>();
            params.put("ipAddress", event.getIpAddress());
            params.put("userAgent", event.getUserAgent());
            params.put("failureReason", event.getFailureReason());
            
            Notification notification = Notification.builder()
                    .eventId(event.getEventId())
                    .eventType(event.getEventType())
                    .notificationType(NotificationType.EMAIL)
                    .priority(NotificationPriority.HIGH)
                    .userId(event.getUserId())
                    .recipient(event.getEmail())
                    .subject("Failed login attempt on your LookForX account")
                    .content("Someone tried to access your account with incorrect credentials.")
                    .templateName("failed-login-alert")
                    .templateParameters(params)
                    .correlationId(event.getCorrelationId())
                    .serviceName(event.getServiceName())
                    .build();
            
            notificationService.sendNotification(notification);
        }
    }
    
    /**
     * Handle form submitted event
     */
    public void handleFormSubmittedEvent(FormSubmittedEvent event) {
        log.info("Processing form submitted event: formId={}, submitter={}", event.getFormSubmissionId(), event.getSubmitterEmail());
        
        Map<String, String> params = new HashMap<>();
        params.put("formName", event.getFormTemplateName());
        params.put("submitterName", event.getSubmitterName());
        params.put("requestId", event.getRequestId());
        params.put("responseCount", event.getResponseCount().toString());
        
        Notification notification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(event.getUserId())
                .recipient(event.getSubmitterEmail())
                .subject("Form submission confirmation - " + event.getFormTemplateName())
                .content("Your form submission has been received and is being processed.")
                .templateName("form-submission-confirmation")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(notification);

        // Bell notification for form submission - handled by bulk notification system
    }
    
    /**
     * Handle request created event
     */
    public void handleRequestCreatedEvent(RequestCreatedEvent event) {
        log.info("Processing request created event: requestId={}, requester={}", event.getRequestId(), event.getRequesterEmail());
        
        Map<String, String> params = new HashMap<>();
        params.put("requestTitle", event.getTitle());
        params.put("categoryName", event.getCategoryName());
        params.put("budget", event.getBudget().toString());
        params.put("currency", event.getCurrency());
        params.put("deadline", event.getDeadline());
        
        Notification notification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(event.getUserId())
                .recipient(event.getRequesterEmail())
                .subject("Request created successfully - " + event.getTitle())
                .content("Your request has been created and is now visible to service providers.")
                .templateName("request-created-confirmation")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(notification);

        // Bell notification for request creation - handled by bulk notification system
    }
    
    /**
     * Handle request status changed event
     */
    public void handleRequestStatusChangedEvent(RequestStatusChangedEvent event) {
        log.info("Processing request status changed event: requestId={}, newStatus={}", event.getRequestId(), event.getNewStatus());
        
        Map<String, String> params = new HashMap<>();
        params.put("requestTitle", event.getRequestTitle());
        params.put("oldStatus", event.getOldStatus());
        params.put("newStatus", event.getNewStatus());
        params.put("changedBy", event.getChangedBy());
        params.put("reason", event.getReason());
        
        // Notify requester
        Notification requesterNotification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getRequesterEmail())
                .subject("Request status updated - " + event.getRequestTitle())
                .content("Your request status has been updated to: " + event.getNewStatus())
                .templateName("request-status-changed")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(requesterNotification);
        
        // Notify assigned provider if applicable
        if (event.getAssignedProviderEmail() != null && !event.getAssignedProviderEmail().isEmpty()) {
            Notification providerNotification = Notification.builder()
                    .eventId(event.getEventId() + "-provider")
                    .eventType(event.getEventType())
                    .notificationType(NotificationType.EMAIL)
                    .priority(NotificationPriority.HIGH)
                    .userId(event.getAssignedProviderId())
                    .recipient(event.getAssignedProviderEmail())
                    .subject("Request assignment - " + event.getRequestTitle())
                    .content("You have been assigned to work on a request.")
                    .templateName("request-assignment")
                    .templateParameters(params)
                    .correlationId(event.getCorrelationId())
                    .serviceName(event.getServiceName())
                    .build();
            
            notificationService.sendNotification(providerNotification);
        }
    }
    
    /**
     * Handle bid placed event
     */
    public void handleBidPlacedEvent(BidPlacedEvent event) {
        log.info("Processing bid placed event: bidId={}, requestId={}", event.getBidId(), event.getRequestId());
        
        Map<String, String> params = new HashMap<>();
        params.put("requestTitle", event.getRequestTitle());
        params.put("bidderName", event.getBidderName());
        params.put("bidAmount", event.getBidAmount().toString());
        params.put("currency", event.getCurrency());
        params.put("proposedDeadline", event.getProposedDeadline());
        params.put("message", event.getMessage());
        
        // Notify requester about new bid
        Notification notification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getRequesterEmail())
                .subject("New bid received - " + event.getRequestTitle())
                .content("You have received a new bid for your request.")
                .templateName("new-bid-notification")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(notification);
    }
    
    /**
     * Handle payment completed event
     */
    public void handlePaymentCompletedEvent(PaymentCompletedEvent event) {
        log.info("Processing payment completed event: paymentId={}, amount={}", event.getPaymentId(), event.getAmount());
        
        Map<String, String> params = new HashMap<>();
        params.put("requestTitle", event.getRequestTitle());
        params.put("amount", event.getAmount().toString());
        params.put("currency", event.getCurrency());
        params.put("paymentMethod", event.getPaymentMethod());
        params.put("transactionId", event.getTransactionId());
        
        // Notify payer
        Notification payerNotification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getPayerEmail())
                .subject("Payment confirmation - " + event.getRequestTitle())
                .content("Your payment has been processed successfully.")
                .templateName("payment-confirmation")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(payerNotification);
        
        // Notify payee
        Notification payeeNotification = Notification.builder()
                .eventId(event.getEventId() + "-payee")
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getPayeeEmail())
                .subject("Payment received - " + event.getRequestTitle())
                .content("You have received a payment for your services.")
                .templateName("payment-received")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(payeeNotification);
    }
    
    /**
     * Handle campaign created event
     */
    public void handleCampaignCreatedEvent(CampaignCreatedEvent event) {
        log.info("Processing campaign created event: campaignId={}, creator={}", event.getCampaignId(), event.getCreatorEmail());
        
        Map<String, String> params = new HashMap<>();
        params.put("campaignName", event.getCampaignName());
        params.put("campaignType", event.getCampaignType());
        params.put("budget", event.getBudget().toString());
        params.put("currency", event.getCurrency());
        params.put("startDate", event.getStartDate());
        params.put("endDate", event.getEndDate());
        
        Notification notification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(event.getUserId())
                .recipient(event.getCreatorEmail())
                .subject("Campaign created successfully - " + event.getCampaignName())
                .content("Your campaign has been created and is now active.")
                .templateName("campaign-created-confirmation")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(notification);
    }
    
    /**
     * Handle membership upgraded event
     */
    public void handleMembershipUpgradedEvent(MembershipUpgradedEvent event) {
        log.info("Processing membership upgraded event: userId={}, newType={}", event.getUserId(), event.getNewMembershipType());
        
        Map<String, String> params = new HashMap<>();
        params.put("userName", event.getUserName());
        params.put("oldMembershipType", event.getOldMembershipType());
        params.put("newMembershipType", event.getNewMembershipType());
        params.put("upgradeFee", event.getUpgradeFee().toString());
        params.put("currency", event.getCurrency());
        params.put("validUntil", event.getValidUntil());
        params.put("benefits", event.getBenefits());
        
        Notification notification = Notification.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.HIGH)
                .userId(event.getUserId())
                .recipient(event.getUserEmail())
                .subject("Membership upgraded successfully")
                .content("Your membership has been upgraded to " + event.getNewMembershipType())
                .templateName("membership-upgraded")
                .templateParameters(params)
                .correlationId(event.getCorrelationId())
                .serviceName(event.getServiceName())
                .build();
        
        notificationService.sendNotification(notification);
    }

    /**
     * Handle user welcome event - creates welcome notification for new users
     */
    public void handleUserWelcomeEvent(UserWelcomeEvent event) {
        log.info("Processing user welcome event: userId={}, email={}, registrationMethod={}",
                event.getUserId(), event.getUserEmail(), event.getRegistrationMethod());

        try {
            log.info("Creating welcome notification for userId: {}", event.getUserId());

            // Create welcome user notification with multi-language support
            Map<String, String> welcomeTitles = new HashMap<>();
            welcomeTitles.put("EN", "Welcome to LookForX!");
            welcomeTitles.put("TR", "LookForX'e Hoş Geldiniz!");
            welcomeTitles.put("DE", "Willkommen bei LookForX!");
            welcomeTitles.put("FR", "Bienvenue sur LookForX!");
            welcomeTitles.put("ES", "¡Bienvenido a LookForX!");
            welcomeTitles.put("IT", "Benvenuto su LookForX!");
            welcomeTitles.put("PT", "Bem-vindo ao LookForX!");
            welcomeTitles.put("RU", "Добро пожаловать в LookForX!");
            welcomeTitles.put("AR", "مرحباً بك في LookForX!");
            welcomeTitles.put("ZH", "欢迎来到LookForX!");

            userNotificationService.createUserNotification(
                    event.getUserId(),
                    welcomeTitles, // titles
                    createWelcomeMessages(event.getUserName(), event.getRegistrationMethod()), // messages
                    "WELCOME_NEW_USER", // notification type
                    "MEDIUM", // priority
                    null, // actionUrl
                    null, // iconUrl
                    "USER", // relatedEntityType
                    event.getUserId().toString() // relatedEntityId
            );

            log.info("Welcome notification created successfully for user: {}", event.getUserEmail());

        } catch (Exception e) {
            log.error("Failed to create welcome notification for user: {}, error: {}",
                    event.getUserEmail(), e.getMessage(), e);
        }
    }

    /**
     * Create welcome messages for all supported languages
     */
    private Map<String, String> createWelcomeMessages(String userName, String registrationMethod) {
        Map<String, String> messages = new HashMap<>();

        messages.put("EN", String.format("Welcome %s! Thank you for joining LookForX via %s. Start exploring our platform and discover amazing opportunities!", userName, registrationMethod));
        messages.put("TR", String.format("Hoş geldin %s! %s ile LookForX'e katıldığın için teşekkürler. Platformumuzu keşfetmeye başla ve harika fırsatları keşfet!", userName, registrationMethod));
        messages.put("DE", String.format("Willkommen %s! Danke, dass du über %s zu LookForX gekommen bist. Beginne unsere Plattform zu erkunden und entdecke großartige Möglichkeiten!", userName, registrationMethod));
        messages.put("FR", String.format("Bienvenue %s! Merci de rejoindre LookForX via %s. Commence à explorer notre plateforme et découvre des opportunités incroyables!", userName, registrationMethod));
        messages.put("ES", String.format("¡Bienvenido %s! Gracias por unirte a LookForX a través de %s. ¡Comienza a explorar nuestra plataforma y descubre oportunidades increíbles!", userName, registrationMethod));
        messages.put("IT", String.format("Benvenuto %s! Grazie per esserti unito a LookForX tramite %s. Inizia a esplorare la nostra piattaforma e scopri opportunità incredibili!", userName, registrationMethod));
        messages.put("PT", String.format("Bem-vindo %s! Obrigado por se juntar ao LookForX via %s. Comece a explorar nossa plataforma e descubra oportunidades incríveis!", userName, registrationMethod));
        messages.put("RU", String.format("Добро пожаловать %s! Спасибо за присоединение к LookForX через %s. Начните изучать нашу платформу и откройте для себя удивительные возможности!", userName, registrationMethod));
        messages.put("AR", String.format("مرحباً %s! شكراً لانضمامك إلى LookForX عبر %s. ابدأ في استكشاف منصتنا واكتشف الفرص المذهلة!", userName, registrationMethod));
        messages.put("ZH", String.format("欢迎 %s！感谢您通过 %s 加入LookForX。开始探索我们的平台，发现令人惊叹的机会！", userName, registrationMethod));

        return messages;
    }
}
