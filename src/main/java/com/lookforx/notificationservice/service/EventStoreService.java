package com.lookforx.notificationservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.events.BaseEvent;
import com.lookforx.notificationservice.domain.EventStore;
import com.lookforx.notificationservice.repository.EventStoreRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for Event Store operations - handles event sourcing and replay functionality
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EventStoreService {
    
    private final EventStoreRepository eventStoreRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * Store an event in the event store
     */
    public EventStore storeEvent(BaseEvent event, String topic, Integer partition, Long offset) {
        try {
            String eventData = objectMapper.writeValueAsString(event);
            
            EventStore eventStore = EventStore.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .serviceName(event.getServiceName())
                .aggregateId(extractAggregateId(event))
                .aggregateType(extractAggregateType(event))
                .version(1L) // Will be incremented for same aggregate
                .timestamp(event.getTimestamp())
                .userId(event.getUserId())
                .correlationId(event.getCorrelationId())
                .eventData(eventData)
                .metadata(event.getMetadata())
                .topic(topic)
                .partition(partition)
                .offset(offset)
                .status(EventStore.EventStatus.RECEIVED)
                .retryCount(0)
                .isReplayed(false)
                .build();
            
            // Set version based on existing events for same aggregate
            if (eventStore.getAggregateId() != null && eventStore.getAggregateType() != null) {
                List<EventStore> existingEvents = eventStoreRepository
                    .findByAggregateIdAndAggregateTypeOrderByVersionAsc(
                        eventStore.getAggregateId(), 
                        eventStore.getAggregateType()
                    );
                eventStore.setVersion((long) (existingEvents.size() + 1));
            }
            
            EventStore saved = eventStoreRepository.save(eventStore);
            log.info("Event stored: eventId={}, type={}, aggregate={}:{}", 
                saved.getEventId(), saved.getEventType(), saved.getAggregateType(), saved.getAggregateId());
            
            return saved;
            
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize event: eventId={}", event.getEventId(), e);
            throw new RuntimeException("Failed to store event", e);
        }
    }
    
    /**
     * Update event status
     */
    public void updateEventStatus(String eventId, EventStore.EventStatus status, String errorMessage) {
        Optional<EventStore> eventOpt = eventStoreRepository.findByEventId(eventId);
        if (eventOpt.isPresent()) {
            EventStore event = eventOpt.get();
            event.setStatus(status);
            event.setProcessedAt(LocalDateTime.now());
            
            if (errorMessage != null) {
                event.setErrorMessage(errorMessage);
                event.setRetryCount(event.getRetryCount() + 1);
            }
            
            eventStoreRepository.save(event);
            log.debug("Event status updated: eventId={}, status={}", eventId, status);
        }
    }
    
    /**
     * Get events for replay by date range
     */
    public List<EventStore> getEventsForReplay(LocalDateTime startDate, LocalDateTime endDate) {
        return eventStoreRepository.findEventsForReplay(startDate, endDate);
    }
    
    /**
     * Get events for replay by service and date range
     */
    public List<EventStore> getEventsForReplayByService(String serviceName, LocalDateTime startDate, LocalDateTime endDate) {
        return eventStoreRepository.findEventsForReplayByService(serviceName, startDate, endDate);
    }
    
    /**
     * Get events for replay by aggregate
     */
    public List<EventStore> getEventsForReplayByAggregate(String aggregateId, String aggregateType, 
                                                         LocalDateTime startDate, LocalDateTime endDate) {
        return eventStoreRepository.findEventsForReplayByAggregate(aggregateId, aggregateType, startDate, endDate);
    }
    
    /**
     * Mark events as replayed
     */
    public void markEventsAsReplayed(List<String> eventIds, String replayId) {
        for (String eventId : eventIds) {
            Optional<EventStore> eventOpt = eventStoreRepository.findByEventId(eventId);
            if (eventOpt.isPresent()) {
                EventStore event = eventOpt.get();
                event.setIsReplayed(true);
                event.setReplayId(replayId);
                event.setReplayedAt(LocalDateTime.now());
                event.setStatus(EventStore.EventStatus.REPLAYED);
                eventStoreRepository.save(event);
            }
        }
        log.info("Marked {} events as replayed with replayId={}", eventIds.size(), replayId);
    }
    
    /**
     * Get events by service
     */
    public Page<EventStore> getEventsByService(String serviceName, Pageable pageable) {
        return eventStoreRepository.findByServiceNameOrderByTimestampDesc(serviceName, pageable);
    }
    
    /**
     * Get events by user
     */
    public Page<EventStore> getEventsByUser(Long userId, Pageable pageable) {
        return eventStoreRepository.findByUserIdOrderByTimestampDesc(userId, pageable);
    }
    
    /**
     * Get events by type
     */
    public Page<EventStore> getEventsByType(String eventType, Pageable pageable) {
        return eventStoreRepository.findByEventTypeOrderByTimestampDesc(eventType, pageable);
    }
    
    /**
     * Get events by status
     */
    public Page<EventStore> getEventsByStatus(EventStore.EventStatus status, Pageable pageable) {
        return eventStoreRepository.findByStatusOrderByTimestampDesc(status, pageable);
    }
    
    /**
     * Get events by date range
     */
    public Page<EventStore> getEventsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return eventStoreRepository.findByTimestampBetweenOrderByTimestampDesc(startDate, endDate, pageable);
    }

    /**
     * Get event by ID
     */
    public Optional<EventStore> getEventById(String eventId) {
        return eventStoreRepository.findByEventId(eventId);
    }
    
    /**
     * Get failed events for retry
     */
    public List<EventStore> getFailedEventsForRetry(int maxRetryCount) {
        return eventStoreRepository.findFailedEventsForRetry(maxRetryCount);
    }
    
    /**
     * Get event statistics
     */
    public EventStatistics getEventStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        long totalEvents = eventStoreRepository.countByTimestampBetween(startDate, endDate);
        long processedEvents = eventStoreRepository.countByStatus(EventStore.EventStatus.PROCESSED);
        long failedEvents = eventStoreRepository.countByStatus(EventStore.EventStatus.FAILED);
        long replayedEvents = eventStoreRepository.countByStatus(EventStore.EventStatus.REPLAYED);
        
        return EventStatistics.builder()
            .totalEvents(totalEvents)
            .processedEvents(processedEvents)
            .failedEvents(failedEvents)
            .replayedEvents(replayedEvents)
            .startDate(startDate)
            .endDate(endDate)
            .build();
    }
    
    /**
     * Extract aggregate ID from event
     */
    private String extractAggregateId(BaseEvent event) {
        // Extract aggregate ID based on event type
        return switch (event.getEventType()) {
            case "USER_REGISTERED", "USER_LOGIN" -> event.getUserId() != null ? event.getUserId().toString() : null;
            case "REQUEST_CREATED", "REQUEST_STATUS_CHANGED" -> extractFieldFromEvent(event, "requestId");
            case "FORM_SUBMITTED" -> extractFieldFromEvent(event, "formId");
            case "BID_PLACED" -> extractFieldFromEvent(event, "bidId");
            case "CAMPAIGN_CREATED" -> extractFieldFromEvent(event, "campaignId");
            case "MEMBERSHIP_UPGRADED" -> event.getUserId() != null ? event.getUserId().toString() : null;
            default -> null;
        };
    }
    
    /**
     * Extract aggregate type from event
     */
    private String extractAggregateType(BaseEvent event) {
        return switch (event.getEventType()) {
            case "USER_REGISTERED", "USER_LOGIN", "MEMBERSHIP_UPGRADED" -> "User";
            case "REQUEST_CREATED", "REQUEST_STATUS_CHANGED" -> "Request";
            case "FORM_SUBMITTED" -> "Form";
            case "BID_PLACED" -> "Bid";
            case "CAMPAIGN_CREATED" -> "Campaign";
            default -> "Unknown";
        };
    }
    
    /**
     * Extract field from event using reflection
     */
    private String extractFieldFromEvent(BaseEvent event, String fieldName) {
        try {
            var field = event.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(event);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("Failed to extract field {} from event {}", fieldName, event.getEventType());
            return null;
        }
    }
    
    /**
     * Event statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class EventStatistics {
        private long totalEvents;
        private long processedEvents;
        private long failedEvents;
        private long replayedEvents;
        private LocalDateTime startDate;
        private LocalDateTime endDate;
    }
}
