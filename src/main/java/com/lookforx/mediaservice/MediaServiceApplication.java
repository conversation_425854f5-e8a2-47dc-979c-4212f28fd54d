package com.lookforx.mediaservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Entry point for the Media Service Spring Boot application.
 * <p>
 * This application exposes REST endpoints for media file management
 * (upload, download, update, delete) using Cloudflare R2 storage.
 * <p>
 * The {@link EnableDiscoveryClient} annotation allows this service to register
 * with a service registry (e.g. Eureka, Consul) for discovery in a microservices environment.
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.lookforx.common.client")
public class MediaServiceApplication {

    /**
     * Main method used to launch the Spring Boot application.
     *
     * @param args runtime arguments passed to the application
     */
    public static void main(String[] args) {
        SpringApplication.run(MediaServiceApplication.class, args);
    }

}
