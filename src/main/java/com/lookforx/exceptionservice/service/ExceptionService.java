package com.lookforx.exceptionservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.exception.DuplicateExceptionCode;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExceptionService {

    private final ExceptionRepository exceptionRepository;
    
    /**
     * <PERSON><PERSON><PERSON> bir exception code ve dil kodu için hata mesajını döner.
     * İstenen dilde mesaj bulunamazsa, İngilizce mesajı döner.
     * İngilizce mesaj da bulunamazsa, varsayılan mesajı döner.
     *
     * @param exceptionCode Hata kodu
     * @param languageCode Dil kodu
     * @return Hata mesajı
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "exception-messages", key = "#exceptionCode + '_' + #languageCode")
    public String getExceptionMessage(String exceptionCode, LanguageCode languageCode) {
        log.debug("Getting exception message for code: {} and language: {}", exceptionCode, languageCode);

        // 1) Kodun tanımlı olduğundan emin ol
        exceptionRepository.findByExceptionCode(exceptionCode)
                .orElseThrow(() -> new ExceptionNotFoundException(exceptionCode));

        // Doğrudan repository'den mesajı almayı dene (fallback ile)
        return exceptionRepository.findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN)
                .orElseGet(() -> getDefaultMessage(languageCode));
    }
    
    @Transactional(readOnly = true)
    public List<ExceptionResponse> getAllExceptions() {
        log.debug("Getting all exceptions from database");

        // Get from database
        List<ExceptionResponse> exceptions = exceptionRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());

        return exceptions;
    }

    @Transactional(readOnly = true)
    public Page<ExceptionResponse> getAllExceptions(Pageable pageable, String search) {
        log.debug("Getting paginated exceptions with search: {}", search);

        Page<ExceptionEntity> entities;
        if (search != null && !search.trim().isEmpty()) {
            entities = exceptionRepository.findByExceptionCodeContainingIgnoreCase(search.trim(), pageable);
        } else {
            entities = exceptionRepository.findAll(pageable);
        }

        return entities.map(this::mapToResponse);
    }

    @Transactional(readOnly = true)
    public ExceptionResponse getExceptionById(Long id) {
        log.debug("Getting exception by id: {}", id);

        // Get from database
        ExceptionEntity exception = exceptionRepository.findById(id)
                .orElseThrow(() -> new ExceptionNotFoundException(id));

        return mapToResponse(exception);
    }

    @Transactional
    @CacheEvict(value = "exception-messages", allEntries = true)
    public ExceptionResponse createException(ExceptionRequest request) {

        exceptionRepository.findByExceptionCode(request.getExceptionCode())
                .ifPresent(e -> { throw new DuplicateExceptionCode(request.getExceptionCode()); });

        ExceptionEntity exception = new ExceptionEntity();
        exception.setExceptionCode(request.getExceptionCode());
        exception.setTranslations(request.getMessages());
        exception.setHttpStatus(request.getHttpStatus());
        
        ExceptionEntity saved = exceptionRepository.save(exception);
        ExceptionResponse response = mapToResponse(saved);

        log.info("Created new exception with code: {}", request.getExceptionCode());
        return response;
    }

    @Transactional
    @CacheEvict(value = "exception-messages", allEntries = true)
    public ExceptionResponse updateException(Long id, ExceptionRequest request) {
        ExceptionEntity exception = exceptionRepository.findById(id)
                .orElseThrow(() -> new ExceptionNotFoundException(id));
        
        exception.setExceptionCode(request.getExceptionCode());
        exception.setTranslations(request.getMessages());
        exception.setHttpStatus(request.getHttpStatus());

        ExceptionEntity updated = exceptionRepository.save(exception);
        ExceptionResponse response = mapToResponse(updated);

        log.info("Updated exception with id: {} and code: {}", id, request.getExceptionCode());
        return response;
    }

    @Transactional
    @CacheEvict(value = "exception-messages", allEntries = true)
    public void deleteException(Long id) {
        if (!exceptionRepository.existsById(id)) {
            throw new ExceptionNotFoundException(id);
        }

        exceptionRepository.deleteById(id);
        log.info("Deleted exception with id: {}", id);
    }
    
    private ExceptionResponse mapToResponse(ExceptionEntity exception) {
        return ExceptionResponse.builder()
                .id(exception.getId())
                .exceptionCode(exception.getExceptionCode())
                // Convert PersistentMap to HashMap to avoid serialization issues
                .messages(new HashMap<>(exception.getTranslations()))
                .httpStatus(exception.getHttpStatus())
                .build();
    }
    
    private String getDefaultMessage(LanguageCode languageCode) {
        Map<LanguageCode, String> defaultMessages = new HashMap<>();
        
        // Temel diller
        defaultMessages.put(LanguageCode.EN, "An error occurred. Please contact support.");
        defaultMessages.put(LanguageCode.TR, "Bir hata oluştu. Lütfen destek ekibiyle iletişime geçin.");
        defaultMessages.put(LanguageCode.DE, "Ein Fehler ist aufgetreten. Bitte kontaktieren Sie den Support.");
        defaultMessages.put(LanguageCode.FR, "Une erreur s'est produite. Veuillez contacter le support.");
        
        // Diğer diller
        defaultMessages.put(LanguageCode.ES, "Se ha producido un error. Por favor, contacte con soporte.");
        defaultMessages.put(LanguageCode.ZH, "发生错误。请联系支持。");
        defaultMessages.put(LanguageCode.HI, "एक त्रुटि हुई है। कृपया सहायता से संपर्क करें।");
        defaultMessages.put(LanguageCode.RU, "Произошла ошибка. Пожалуйста, свяжитесь с поддержкой.");
        defaultMessages.put(LanguageCode.AR, "حدث خطأ. يرجى الاتصال بالدعم.");
        defaultMessages.put(LanguageCode.PT, "Ocorreu um erro. Entre em contato com o suporte.");
        defaultMessages.put(LanguageCode.IT, "Si è verificato un errore. Si prega di contattare l'assistenza.");
        defaultMessages.put(LanguageCode.JA, "エラーが発生しました。サポートにお問い合わせください。");
        defaultMessages.put(LanguageCode.KO, "오류가 발생했습니다. 지원팀에 문의하십시오.");
        defaultMessages.put(LanguageCode.NL, "Er is een fout opgetreden. Neem contact op met ondersteuning.");
        defaultMessages.put(LanguageCode.PL, "Wystąpił błąd. Prosimy o kontakt z pomocą techniczną.");
        defaultMessages.put(LanguageCode.DA, "Der opstod en fejl. Kontakt venligst support.");
        defaultMessages.put(LanguageCode.SV, "Ett fel inträffade. Vänligen kontakta support.");
        defaultMessages.put(LanguageCode.NO, "Det oppstod en feil. Vennligst kontakt support.");
        defaultMessages.put(LanguageCode.FI, "Tapahtui virhe. Ota yhteyttä tukeen.");
        defaultMessages.put(LanguageCode.CS, "Došlo k chybě. Kontaktujte prosím podporu.");
        defaultMessages.put(LanguageCode.HU, "Hiba történt. Kérjük, lépjen kapcsolatba az ügyfélszolgálattal.");
        defaultMessages.put(LanguageCode.RO, "A apărut o eroare. Vă rugăm să contactați asistența.");
        defaultMessages.put(LanguageCode.EL, "Παρουσιάστηκε σφάλμα. Επικοινωνήστε με την υποστήριξη.");
        defaultMessages.put(LanguageCode.TH, "<lemma ไม่ สามารถ ต่อฝ่าย ได้ โปรด ต่อฝ่าย ได้");
        defaultMessages.put(LanguageCode.VI, "Đã xảy ra lỗi. Vui lòng liên hệ bộ phận hỗ trợ.");
        defaultMessages.put(LanguageCode.ID, "Terjadi kesalahan. Silakan hubungi dukungan.");
        defaultMessages.put(LanguageCode.MS, "Ralat telah berlaku. Sila hubungi sokongan.");
        defaultMessages.put(LanguageCode.HE, "אירעה שגיאה. אנא צור קשר עם התמיכה.");
        defaultMessages.put(LanguageCode.UR, "ایک خرابی پیش آگئی ہے۔ براہ کرم سپورٹ سے رابطہ کریں۔");
        defaultMessages.put(LanguageCode.FA, "خطایی رخ داده است. لطفا با پشتیبانی تماس بگیرید.");
        defaultMessages.put(LanguageCode.BN, "একটি ত্রুটি ঘটেছে। অনুগ্রহ করে সাপোর্টের সাথে যোগাযোগ করুন।");
        defaultMessages.put(LanguageCode.PA, "ਇੱਕ ਗਲਤੀ ਹੋਈ ਹੈ। ਕਿਰਪਾ ਕਰਕੇ ਸਹਾਇਤਾ ਨਾਲ ਸੰਪਰਕ ਕਰੋ।");
        defaultMessages.put(LanguageCode.SQ, "Ndodhi një gabim. Ju lutemi kontaktoni mbështetjen.");
        defaultMessages.put(LanguageCode.BS, "Došlo je do greške. Molimo kontaktirajte podršku.");
        defaultMessages.put(LanguageCode.HR, "Došlo je do pogreške. Molimo kontaktirajte podršku.");
        defaultMessages.put(LanguageCode.SK, "Vyskytla sa chyba. Kontaktujte prosím podporu.");
        defaultMessages.put(LanguageCode.SL, "Prišlo je do napake. Prosimo, kontaktirajte podporo.");
        defaultMessages.put(LanguageCode.LT, "Įvyko klaida. Prašome susisiekti su palaikymo tarnyba.");
        defaultMessages.put(LanguageCode.LV, "Radās kļūda. Lūdzu, sazinieties ar atbalsta dienestu.");
        defaultMessages.put(LanguageCode.ET, "Tekkis viga. Palun võtke ühendust toega.");
        defaultMessages.put(LanguageCode.BG, "Възникна грешка. Моля, свържете се с поддръжката.");
        defaultMessages.put(LanguageCode.MK, "Се случи грешка. Ве молиме контактирајте ја поддршката.");
        defaultMessages.put(LanguageCode.SR, "Дошло је до грешке. Молимо контактирајте подршку.");
        defaultMessages.put(LanguageCode.CY, "Digwyddodd gwall. Cysylltwch â chymorth os gwelwch yn dda.");
        defaultMessages.put(LanguageCode.GA, "Tharla earráid. Déan teagmháil le tacaíocht.");
        defaultMessages.put(LanguageCode.IS, "Villa kom upp. Vinsamlegast hafðu samband við stuðning.");
        defaultMessages.put(LanguageCode.GL, "Produciuse un erro. Póñase en contacto co soporte.");
        defaultMessages.put(LanguageCode.KY, "Ката кетти. Колдоо кызматына кайрылыңыз.");
        defaultMessages.put(LanguageCode.NE, "त्रुटि भयो। कृपया सहायतामा सम्पर्क गर्नुहोस्।");
        defaultMessages.put(LanguageCode.KM, "មានកំហុស។ សូមទាក់ទងផ្នែកគាំទ្រ។");
        defaultMessages.put(LanguageCode.LA, "Error accidit. Quaeso, contactus auxilium.");
        defaultMessages.put(LanguageCode.MY, "အမှားတစ်ခုဖြစ်ပွားခဲ့သည်။ ကျေးဇူးပြု၍ အထောက်အပံ့ကို ဆက်သွယ်ပါ။");
        defaultMessages.put(LanguageCode.MN, "Алдаа гарлаа. Дэмжлэгтэй холбогдоно уу.");
        defaultMessages.put(LanguageCode.TG, "Хатогӣ рух дод. Лутфан бо дастгирӣ тамос гиред.");
        defaultMessages.put(LanguageCode.UZ, "Xatolik yuz berdi. Iltimos, qo'llab-quvvatlash xizmatiga murojaat qiling.");
        defaultMessages.put(LanguageCode.AZ, "Xəta baş verdi. Dəstəklə əlaqə saxlayın.");
        defaultMessages.put(LanguageCode.HY, "Սխալ է տեղի ունեցել: Խնդրում ենք կապվել աջակցության հետ:");
        defaultMessages.put(LanguageCode.BE, "Адбылася памылка. Калі ласка, звярніцеся ў службу падтрымкі.");
        defaultMessages.put(LanguageCode.UK, "Сталася помилка. Будь ласка, зверніться до служби підтримки.");
        
        return defaultMessages.getOrDefault(languageCode, defaultMessages.get(LanguageCode.EN));
    }
}