package com.lookforx.exceptionservice.exception;

import com.lookforx.common.exception.BaseException;
import org.springframework.http.HttpStatus;

import java.io.Serial;

/**
 * Exception entity bulunamadığında fırlatılan exception.
 */
public class ExceptionNotFoundException extends BaseException {

    @Serial
    private static final long serialVersionUID = -8022123804054558025L;

    public static final HttpStatus STATUS = HttpStatus.NOT_FOUND;

    private static final String ERROR_CODE = "EXCEPTION_NOT_FOUND";
    private static final String DEFAULT_MESSAGE_FOR_ID = "Exception not found with id: ";
    private static final String DEFAULT_MESSAGE_FOR_CODE = "Exception not found with code: ";

    public ExceptionNotFoundException(Long id) {
        super(ERROR_CODE, DEFAULT_MESSAGE_FOR_ID + id, HttpStatus.NOT_FOUND, id);
    }

    public ExceptionNotFoundException(String exceptionCode) {
        super(ERRO<PERSON>_CODE, DEFAULT_MESSAGE_FOR_CODE + exceptionCode, HttpStatus.NOT_FOUND, exceptionCode);
    }

}