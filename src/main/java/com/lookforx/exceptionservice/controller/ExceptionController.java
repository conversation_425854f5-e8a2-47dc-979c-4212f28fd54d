package com.lookforx.exceptionservice.controller;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionMessageResponse;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.service.ExceptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing application exceptions.
 * <p>
 * Provides CRUD operations for exception definitions and lookup for localized exception messages.
 */
@RestController
@RequestMapping("/api/v1/exceptions")
@RequiredArgsConstructor
@Tag(name = "Exceptions", description = "API for exception management and lookup")
public class ExceptionController {

    private final ExceptionService exceptionService;

    /**
     * Retrieve all defined exceptions with pagination and search.
     *
     * @param page page number (0-based)
     * @param size page size
     * @param sortBy field to sort by
     * @param sortDir sort direction (asc/desc)
     * @param search search term for exception code or messages
     * @param unpaged whether to return all results without pagination
     * @return paginated list of exception definitions or all exceptions if unpaged=true
     */
    @Operation(summary = "Get all exceptions with pagination", description = "Returns a paginated list of exception definitions with optional search.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list",
                    content = @Content(mediaType = "application/json"))
    })
    @GetMapping
    public ResponseEntity<?> getAllExceptions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "false") boolean unpaged) {

        if (unpaged) {
            return ResponseEntity.ok(exceptionService.getAllExceptions());
        }

        // Create pageable
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get paginated results
        Page<ExceptionResponse> result = exceptionService.getAllExceptions(pageable, search);
        return ResponseEntity.ok(result);
    }

    /**
     * Create a new exception definition.
     *
     * @param request the exception details to create
     * @return the created exception definition
     */
    @Operation(summary = "Create exception", description = "Creates a new exception definition.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Exception created successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PostMapping
    public ResponseEntity<ExceptionResponse> createException(@Valid @RequestBody ExceptionRequest request) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(exceptionService.createException(request));
    }

    /**
     * Get an exception definition by ID.
     *
     * @param id the ID of the exception to retrieve
     * @return the exception definition
     */
    @Operation(summary = "Get exception by ID", description = "Retrieves an exception definition by its ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Exception found",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ExceptionResponse> getExceptionById(@PathVariable Long id) {
        return ResponseEntity.ok(exceptionService.getExceptionById(id));
    }

    /**
     * Update an existing exception definition.
     *
     * @param id      the ID of the exception to update
     * @param request the updated exception details
     * @return the updated exception definition
     */
    @Operation(summary = "Update exception", description = "Updates an existing exception definition by ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Exception updated successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    public ResponseEntity<ExceptionResponse> updateException(
            @PathVariable Long id,
            @Valid @RequestBody ExceptionRequest request) {
        return ResponseEntity.ok(exceptionService.updateException(id, request));
    }

    /**
     * Delete an exception definition.
     *
     * @param id the ID of the exception to delete
     */
    @Operation(summary = "Delete exception", description = "Deletes an exception definition by ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Exception deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Exception not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteException(@PathVariable Long id) {
        exceptionService.deleteException(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Lookup the localized message for a given exception code.
     *
     * @param exceptionCode the code of the exception
     * @param languageCode  the target language for the message
     * @return the localized exception message
     */
    @Operation(summary = "Get exception message", description = "Retrieves a localized exception message by exception code and language.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Message retrieved successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionMessageResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception code not found")
    })
    @GetMapping("/exception-message")
    public ResponseEntity<ExceptionMessageResponse> getExceptionMessage(
            @RequestParam String exceptionCode,
            @RequestParam LanguageCode languageCode) {
        String message = exceptionService.getExceptionMessage(exceptionCode, languageCode);
        ExceptionMessageResponse response = ExceptionMessageResponse.builder()
                .exceptionCode(exceptionCode)
                .languageCode(languageCode)
                .message(message)
                .build();
        return ResponseEntity.ok(response);
    }

}