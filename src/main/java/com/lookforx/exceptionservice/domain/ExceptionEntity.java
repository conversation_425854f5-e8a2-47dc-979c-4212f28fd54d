package com.lookforx.exceptionservice.domain;

import com.lookforx.common.entity.BaseEntity;
import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

@Entity
@Table(name = "exceptions")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionEntity extends BaseEntity {
    
    @Column(name = "exception_code", nullable = false, unique = true)
    private String exceptionCode;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
            name = "exception_translations",
            joinColumns = @JoinColumn(name = "exception_id"),
            indexes = {
                    @Index(name = "idx_language_code", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "message")
    @Builder.Default
    private Map<LanguageCode, String> translations = new HashMap<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "http_status")
    @Builder.Default
    private HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
}
