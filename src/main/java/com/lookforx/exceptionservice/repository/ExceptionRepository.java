package com.lookforx.exceptionservice.repository;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;


public interface ExceptionRepository extends JpaRepository<ExceptionEntity, Long> {
    
    /**
     * ExceptionCode'a göre exception entity'sini bulur
     *
     * @param exceptionCode Exception kodu
     * @return Exception entity
     */
    Optional<ExceptionEntity> findByExceptionCode(String exceptionCode);

    /**
     * ExceptionCode'da arama yapar (case insensitive)
     *
     * @param exceptionCode Exception kodu (partial match)
     * @param pageable Pagination bilgisi
     * @return Paginated exception entities
     */
    Page<ExceptionEntity> findByExceptionCodeContainingIgnoreCase(String exceptionCode, Pageable pageable);
    
    /**
     * ExceptionCode ve LanguageCode'a göre ilgili mesajı bulur
     * 
     * @param exceptionCode Exception kodu
     * @param languageCode Dil kodu
     * @return İlgili dildeki mesaj
     */
    @Query("SELECT e.translations[:languageCode] FROM ExceptionEntity e WHERE e.exceptionCode = :exceptionCode")
    Optional<String> findMessageByExceptionCodeAndLanguageCode(
            @Param("exceptionCode") String exceptionCode,
            @Param("languageCode") LanguageCode languageCode);


    /**
     * Fallback’lı mesaj araması: önce istenen dil, sonra İngilizce
     */
    default Optional<String> findMessageWithFallback(String exceptionCode,
                                                     LanguageCode requested,
                                                     LanguageCode fallback) {
        // 1) Try requested language
        Optional<String> msg = findMessageByExceptionCodeAndLanguageCode(exceptionCode, requested);
        if (msg.isPresent()) {
            return msg;
        }

        // 2) Then fallback (e.g. EN) if different
        if (!requested.equals(fallback)) {
            msg = findMessageByExceptionCodeAndLanguageCode(exceptionCode, fallback);
            if (msg.isPresent()) {
                return msg;
            }
        }

        // 3) None found
        return Optional.empty();

    }

}