package com.lookforx.exceptionservice.dto;

import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * Exception oluşturma veya güncelleme için kullanılan DTO.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionRequest {
    
    /**
     * Exception kodu. Büyük harfler ve alt çizgi (_) içerebilir.
     * Örnek: USER_NOT_FOUND, INVALID_INPUT, PAYMENT_FAILED
     */
    @NotBlank(message = "Exception kodu boş olamaz")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "Exception kodu bü<PERSON><PERSON>k harfler, rakamlar ve alt çizgilerden oluşmalıdır ve bir harf ile başlamalıdır")
    private String exceptionCode;
    
    /**
     * Farklı dillerdeki hata mesajları.
     * Key: Dil kodu (LanguageCode enum'ından)
     * Value: İlgili dildeki hata mesajı
     * 
     * En az bir dil için mesaj belirtilmelidir (tercihen İngilizce).
     */
    @NotEmpty(message = "En az bir dil için hata mesajı belirtilmelidir")
    private Map<LanguageCode, String> messages = new HashMap<>();
    
    /**
     * Exception'ın HTTP durum kodu.
     * Varsayılan olarak INTERNAL_SERVER_ERROR (500).
     */
    @NotNull(message = "HTTP durum kodu belirtilmelidir")
    private HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
}
