package com.lookforx.exceptionservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.util.Map;

/**
 * Exception bilgilerini istemciye döndürmek için kullanılan DTO.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionResponse {
    /**
     * Exception'ın benzersiz tanımlayıcısı
     */
    private Long id;
    
    /**
     * Exception kodu (örn. USER_NOT_FOUND, INVALID_INPUT)
     */
    private String exceptionCode;
    
    /**
     * Farklı dillerdeki hata mesajları
     * Key: Dil kodu
     * Value: İlgili dildeki hata mesajı
     */
    private Map<LanguageCode, String> messages;
    
    /**
     * Exception'ın HTTP durum kodu
     */
    private HttpStatus httpStatus;
    
    /**
     * Exception'ın oluşturulma tarihi (opsiyonel)
     */
    private String createdAt;
    
    /**
     * Exception'ın son güncellenme tarihi (opsiyonel)
     */
    private String updatedAt;
}