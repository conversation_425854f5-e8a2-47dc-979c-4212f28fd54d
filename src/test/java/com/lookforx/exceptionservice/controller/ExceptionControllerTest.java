package com.lookforx.exceptionservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionMessageResponse;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.service.ExceptionService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(
        controllers = ExceptionController.class
)
@AutoConfigureMockMvc(addFilters = false)
class ExceptionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockitoBean
    ExceptionService exceptionService;

    private static final String BASE = "/api/v1/exceptions";

    @Test
    @DisplayName("GET  /api/v1/exceptions → 200 & full ExceptionResponse list")
    void testGetAllExceptions() throws Exception {

        // Given
        ExceptionResponse resp = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("E001")
                .messages(Map.of(LanguageCode.EN, "Something went wrong"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .createdAt("2025-06-23T08:00:00")
                .updatedAt("2025-06-23T08:00:00")
                .build();

        List<ExceptionResponse> list = List.of(resp);

        // When
        when(exceptionService.getAllExceptions()).thenReturn(list);

        // Then
        mockMvc.perform(get(BASE).param("unpaged", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // Verify
        verify(exceptionService, times(1)).getAllExceptions();

    }

    @Test
    @DisplayName("POST /api/v1/exceptions → 201 & created ExceptionResponse")
    void testCreateException() throws Exception {

        // Given
        ExceptionRequest req = new ExceptionRequest();
        req.setExceptionCode("E002");
        req.setMessages(Map.of(LanguageCode.TR, "Bir hata oluştu"));
        req.setHttpStatus(HttpStatus.NOT_FOUND);

        ExceptionResponse created = ExceptionResponse.builder()
                .id(2L)
                .exceptionCode(req.getExceptionCode())
                .messages(req.getMessages())
                .httpStatus(req.getHttpStatus())
                .createdAt("2025-06-23T09:15:00")
                .updatedAt("2025-06-23T09:15:00")
                .build();

        // When
        when(exceptionService.createException(any(ExceptionRequest.class)))
                .thenReturn(created);

        // Then
        mockMvc.perform(post(BASE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(created.getId()))
                .andExpect(jsonPath("$.exceptionCode").value(created.getExceptionCode()))
                .andExpect(jsonPath("$.httpStatus").value(created.getHttpStatus().name()));

        // Verify
        verify(exceptionService, times(1)).createException(any(ExceptionRequest.class));

    }

    @Test
    @DisplayName("PUT  /api/v1/exceptions/{id} → 200 & updated ExceptionResponse")
    void testUpdateException() throws Exception {

        // Given
        Long id = 3L;
        ExceptionRequest req = new ExceptionRequest();
        req.setExceptionCode("E003");
        req.setMessages(Map.of(LanguageCode.EN, "Updated message"));
        req.setHttpStatus(HttpStatus.CONFLICT);

        ExceptionResponse updated = ExceptionResponse.builder()
                .id(id)
                .exceptionCode(req.getExceptionCode())
                .messages(req.getMessages())
                .httpStatus(req.getHttpStatus())
                .createdAt("2025-06-22T14:00:00")
                .updatedAt("2025-06-23T10:30:00")
                .build();

        // When
        when(exceptionService.updateException(eq(id), any(ExceptionRequest.class)))
                .thenReturn(updated);

        // Then
        mockMvc.perform(put(BASE + "/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(updated.getId()))
                .andExpect(jsonPath("$.exceptionCode").value(updated.getExceptionCode()))
                .andExpect(jsonPath("$.httpStatus").value(updated.getHttpStatus().name()));

        // Verify
        verify(exceptionService, times(1)).updateException(eq(id), any(ExceptionRequest.class));

    }

    @Test
    @DisplayName("DELETE /api/v1/exceptions/{id} → 204 No Content")
    void testDeleteException() throws Exception {

        // Given
        Long id = 4L;

        // When & Then
        mockMvc.perform(delete(BASE + "/{id}", id))
                .andExpect(status().isNoContent());

        // Verify
        verify(exceptionService, times(1)).deleteException(eq(id));


    }

    @Test
    @DisplayName("GET  /api/v1/exceptions/exception-message → 200 & ExceptionMessageResponse")
    void testGetExceptionMessage() throws Exception {

        // Given
        String code = "E004";
        LanguageCode lang = LanguageCode.TR;
        String msg = "Özel hata mesajı";

        ExceptionMessageResponse resp = ExceptionMessageResponse.builder()
                .exceptionCode(code)
                .languageCode(lang)
                .message(msg)
                .build();

        // When
        when(exceptionService.getExceptionMessage(eq(code), eq(lang)))
                .thenReturn(msg);


        // Then
        mockMvc.perform(get(BASE + "/exception-message")
                        .param("exceptionCode", code)
                        .param("languageCode", lang.name()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(resp)));

        // Verify
        verify(exceptionService, times(1)).getExceptionMessage(eq(code), eq(lang));

    }

}