package com.lookforx.exceptionservice.service;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExceptionServiceTest {

    @InjectMocks
    private ExceptionService exceptionService;

    @Mock
    private ExceptionRepository exceptionRepository;

    @Test
    void whenExceptionCodeNotFound_shouldThrowExceptionNotFoundException() {

        // Given
        String code = "NOT_EXIST";

        // When
        when(exceptionRepository.findByExceptionCode(code)).thenReturn(Optional.empty());

        // Then
        ExceptionNotFoundException ex = assertThrows(ExceptionNotFoundException.class,
                () -> exceptionService.getExceptionMessage(code, LanguageCode.EN));

        assertTrue(ex.getMessage().contains(code));

        // Verify
        verify(exceptionRepository).findByExceptionCode(code);

    }

    @Test
    void whenTranslationExists_shouldReturnThatTranslation() {

        // Given
        String code = "SOME_CODE";
        String translation = "Translated message";

        // When
        when(exceptionRepository.findByExceptionCode(code)).thenReturn(Optional.of(new ExceptionEntity()));
        when(exceptionRepository.findMessageWithFallback(code, LanguageCode.FR, LanguageCode.EN))
                .thenReturn(Optional.of(translation));

        // Then
        String result = exceptionService.getExceptionMessage(code, LanguageCode.FR);

        // Then
        assertEquals(translation, result);

        // Verify
        verify(exceptionRepository).findByExceptionCode(code);
        verify(exceptionRepository).findMessageWithFallback(code, LanguageCode.FR, LanguageCode.EN);

    }

    @Test
    void whenTranslationMissing_butEnglishExists_shouldReturnEnglish() {

        // Given
        String code = "SOME_CODE";
        String englishMsg = "English fallback message";

        // When
        when(exceptionRepository.findByExceptionCode(code)).thenReturn(Optional.of(new ExceptionEntity()));
        when(exceptionRepository.findMessageWithFallback(code, LanguageCode.HI, LanguageCode.EN))
                .thenReturn(Optional.of(englishMsg));

        // Then
        String result = exceptionService.getExceptionMessage(code, LanguageCode.HI);

        assertEquals(englishMsg, result);

        // Verify
        verify(exceptionRepository).findByExceptionCode(code);
        verify(exceptionRepository).findMessageWithFallback(code, LanguageCode.HI, LanguageCode.EN);

    }

    @Test
    void whenNoTranslationOrFallback_shouldReturnDefaultMessage() {

        // Given
        String code = "SOME_CODE";
        String expected = "Bir hata oluştu. Lütfen destek ekibiyle iletişime geçin.";

        // When
        when(exceptionRepository.findByExceptionCode(code)).thenReturn(Optional.of(new ExceptionEntity()));
        when(exceptionRepository.findMessageWithFallback(code, LanguageCode.TR, LanguageCode.EN))
                .thenReturn(Optional.empty());

        // Then
        String result = exceptionService.getExceptionMessage(code, LanguageCode.TR);

        assertEquals(expected, result);

        // Verify
        verify(exceptionRepository).findByExceptionCode(code);
        verify(exceptionRepository).findMessageWithFallback(code, LanguageCode.TR, LanguageCode.EN);

    }

    @Test
    void whenGetAllExceptions_shouldReturnMappedList() {

        // Given
        ExceptionEntity entity1 = new ExceptionEntity();
        entity1.setId(1L);
        entity1.setExceptionCode("CODE1");
        Map<LanguageCode, String> translations1 = Map.of(LanguageCode.EN, "Message1");
        entity1.setTranslations(translations1);
        entity1.setHttpStatus(HttpStatus.BAD_REQUEST);

        ExceptionEntity entity2 = new ExceptionEntity();
        entity2.setId(2L);
        entity2.setExceptionCode("CODE2");
        Map<LanguageCode, String> translations2 = Map.of(LanguageCode.TR, "Mesaj2");
        entity2.setTranslations(translations2);
        entity2.setHttpStatus(HttpStatus.NOT_FOUND);

        when(exceptionRepository.findAll()).thenReturn(List.of(entity1, entity2));

        // When
        List<ExceptionResponse> result = exceptionService.getAllExceptions();

        // Then
        assertEquals(2, result.size());

        ExceptionResponse resp1 = result.get(0);
        assertEquals(1L, resp1.getId());
        assertEquals("CODE1", resp1.getExceptionCode());
        assertEquals(translations1, resp1.getMessages());
        assertEquals(HttpStatus.BAD_REQUEST, resp1.getHttpStatus());

        ExceptionResponse resp2 = result.get(1);
        assertEquals(2L, resp2.getId());
        assertEquals("CODE2", resp2.getExceptionCode());
        assertEquals(translations2, resp2.getMessages());
        assertEquals(HttpStatus.NOT_FOUND, resp2.getHttpStatus());

        // Verify
        verify(exceptionRepository).findAll();

    }

    @Test
    void whenCreateException_withNewCode_shouldSaveAndReturnResponse() {

        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("NEW_CODE")
                .messages(Map.of(LanguageCode.EN, "New message"))
                .httpStatus(HttpStatus.CREATED)
                .build();

        ExceptionEntity toSave = new ExceptionEntity();
        toSave.setExceptionCode("NEW_CODE");
        toSave.setTranslations(request.getMessages());
        toSave.setHttpStatus(request.getHttpStatus());

        ExceptionEntity saved = new ExceptionEntity();
        saved.setId(10L);
        saved.setExceptionCode("NEW_CODE");
        saved.setTranslations(request.getMessages());
        saved.setHttpStatus(request.getHttpStatus());

        when(exceptionRepository.findByExceptionCode("NEW_CODE")).thenReturn(Optional.empty());
        when(exceptionRepository.save(any(ExceptionEntity.class))).thenReturn(saved);

        // When
        var response = exceptionService.createException(request);

        // Then
        assertNotNull(response);
        assertEquals(10L, response.getId());
        assertEquals("NEW_CODE", response.getExceptionCode());
        assertEquals(request.getMessages(), response.getMessages());
        assertEquals(request.getHttpStatus(), response.getHttpStatus());

        // Verify
        verify(exceptionRepository).findByExceptionCode("NEW_CODE");
        verify(exceptionRepository).save(any(ExceptionEntity.class));

    }

    @Test
    void whenUpdateException_withNonExistingId_shouldThrowExceptionNotFoundException() {

        // Given
        Long id = 42L;

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("CODE")
                .messages(Map.of(LanguageCode.EN, "Msg"))
                .httpStatus(HttpStatus.OK)
                .build();

        // When
        when(exceptionRepository.findById(id)).thenReturn(Optional.empty());

        // Then
        ExceptionNotFoundException ex = assertThrows(ExceptionNotFoundException.class,
                () -> exceptionService.updateException(id, request));

        assertTrue(ex.getMessage().contains(id.toString()));

        // Verify
        verify(exceptionRepository).findById(id);

    }

    @Test
    void whenUpdateException_withExistingEntity_shouldSaveAndReturnUpdatedResponse() {

        // Given
        Long id = 1L;
        ExceptionEntity existing = new ExceptionEntity();
        existing.setId(id);
        existing.setExceptionCode("OLD");
        existing.setTranslations(Map.of(LanguageCode.EN, "Old"));
        existing.setHttpStatus(HttpStatus.BAD_REQUEST);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("NEW")
                .messages(Map.of(LanguageCode.TR, "Yeni"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionEntity updatedEntity = new ExceptionEntity();
        updatedEntity.setId(id);
        updatedEntity.setExceptionCode("NEW");
        updatedEntity.setTranslations(request.getMessages());
        updatedEntity.setHttpStatus(request.getHttpStatus());

        // When
        when(exceptionRepository.findById(id)).thenReturn(Optional.of(existing));
        when(exceptionRepository.save(existing)).thenReturn(updatedEntity);

        // Then
        ExceptionResponse response = exceptionService.updateException(id, request);

        assertNotNull(response);
        assertEquals(id, response.getId());
        assertEquals("NEW", response.getExceptionCode());
        assertEquals(request.getMessages(), response.getMessages());
        assertEquals(request.getHttpStatus(), response.getHttpStatus());

        // Verify
        verify(exceptionRepository).findById(id);
        verify(exceptionRepository).save(existing);

    }

    @Test
    void whenDeleteException_withNonExistingId_shouldThrowExceptionNotFoundException() {

        // Given
        Long id = 99L;

        // When
        when(exceptionRepository.existsById(id)).thenReturn(false);

        // Then
        ExceptionNotFoundException ex = assertThrows(ExceptionNotFoundException.class,
                () -> exceptionService.deleteException(id));

        assertTrue(ex.getMessage().contains(id.toString()));

        // Verify
        verify(exceptionRepository).existsById(id);

    }

    @Test
    void whenDeleteException_withExistingId_shouldCallDelete() {

        // Given
        Long id = 2L;

        // When
        when(exceptionRepository.existsById(id)).thenReturn(true);
        doNothing().when(exceptionRepository).deleteById(id);

        // Then
        exceptionService.deleteException(id);

        // Verify
        verify(exceptionRepository).existsById(id);
        verify(exceptionRepository).deleteById(id);

    }

}