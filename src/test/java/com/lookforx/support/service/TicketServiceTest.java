package com.lookforx.support.service;

import com.lookforx.support.domain.entity.Ticket;
import com.lookforx.support.domain.enums.TicketStatus;
import com.lookforx.support.domain.enums.TicketType;
import com.lookforx.support.dto.request.CreateComplaintTicketRequest;
import com.lookforx.support.dto.request.CreateRequestTicketRequest;
import com.lookforx.support.dto.response.TicketResponse;
import com.lookforx.support.repository.TicketRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for TicketService.
 */
@ExtendWith(MockitoExtension.class)
class TicketServiceTest {

    @Mock
    private TicketRepository ticketRepository;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private TicketService ticketService;

    private UUID testTicketId;
    private UUID testUserId;
    private String testRequestId;
    private String testBidId;

    @BeforeEach
    void setUp() {
        testTicketId = UUID.randomUUID();
        testUserId = UUID.randomUUID();
        testRequestId = "REQ-12345";
        testBidId = "BID-67890";
    }

    @Test
    void createRequestTicket_ShouldCreateAndReturnTicket() {
        // Given
        CreateRequestTicketRequest request = CreateRequestTicketRequest.builder()
                .requestId(testRequestId)
                .title("Test Request Ticket")
                .description("Test description")
                .userId(testUserId)
                .priority("HIGH")
                .build();

        Ticket savedTicket = createTestTicket(TicketType.REQUEST);
        savedTicket.setRequestId(testRequestId);
        savedTicket.setPriority("HIGH");

        when(ticketRepository.save(any(Ticket.class))).thenReturn(savedTicket);

        // When
        TicketResponse response = ticketService.createRequestTicket(request);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getTicketId()).isEqualTo(testTicketId);
        assertThat(response.getType()).isEqualTo(TicketType.REQUEST);
        assertThat(response.getStatus()).isEqualTo(TicketStatus.OPEN);
        assertThat(response.getRequestId()).isEqualTo(testRequestId);
        assertThat(response.getPriority()).isEqualTo("HIGH");

        verify(ticketRepository).save(any(Ticket.class));
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void createComplaintTicket_ShouldCreateAndReturnTicket() {
        // Given
        CreateComplaintTicketRequest request = CreateComplaintTicketRequest.builder()
                .bidId(testBidId)
                .targetType("USER")
                .targetId("USER-123")
                .title("Test Complaint Ticket")
                .description("Test complaint description")
                .userId(testUserId)
                .priority("MEDIUM")
                .build();

        Ticket savedTicket = createTestTicket(TicketType.COMPLAINT);
        savedTicket.setBidId(testBidId);
        savedTicket.setTargetType("USER");
        savedTicket.setTargetId("USER-123");
        savedTicket.setPriority("MEDIUM");

        when(ticketRepository.save(any(Ticket.class))).thenReturn(savedTicket);

        // When
        TicketResponse response = ticketService.createComplaintTicket(request);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getTicketId()).isEqualTo(testTicketId);
        assertThat(response.getType()).isEqualTo(TicketType.COMPLAINT);
        assertThat(response.getStatus()).isEqualTo(TicketStatus.OPEN);
        assertThat(response.getBidId()).isEqualTo(testBidId);
        assertThat(response.getTargetType()).isEqualTo("USER");
        assertThat(response.getTargetId()).isEqualTo("USER-123");
        assertThat(response.getPriority()).isEqualTo("MEDIUM");

        verify(ticketRepository).save(any(Ticket.class));
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void getTicketById_WhenTicketExists_ShouldReturnTicket() {
        // Given
        Ticket ticket = createTestTicket(TicketType.REQUEST);
        when(ticketRepository.findById(testTicketId)).thenReturn(Optional.of(ticket));

        // When
        TicketResponse response = ticketService.getTicketById(testTicketId);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getTicketId()).isEqualTo(testTicketId);
        assertThat(response.getType()).isEqualTo(TicketType.REQUEST);

        verify(ticketRepository).findById(testTicketId);
    }

    @Test
    void getTicketById_WhenTicketNotExists_ShouldThrowException() {
        // Given
        when(ticketRepository.findById(testTicketId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> ticketService.getTicketById(testTicketId))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Ticket not found with ID");

        verify(ticketRepository).findById(testTicketId);
    }

    @Test
    void getTicketByRequestId_WhenTicketExists_ShouldReturnTicket() {
        // Given
        Ticket ticket = createTestTicket(TicketType.REQUEST);
        ticket.setRequestId(testRequestId);
        when(ticketRepository.findByRequestId(testRequestId)).thenReturn(Optional.of(ticket));

        // When
        TicketResponse response = ticketService.getTicketByRequestId(testRequestId);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(testRequestId);

        verify(ticketRepository).findByRequestId(testRequestId);
    }

    @Test
    void updateTicketStatus_ShouldUpdateStatusAndPublishEvent() {
        // Given
        Ticket ticket = createTestTicket(TicketType.REQUEST);
        ticket.setStatus(TicketStatus.OPEN);
        
        Ticket updatedTicket = createTestTicket(TicketType.REQUEST);
        updatedTicket.setStatus(TicketStatus.RESOLVED);
        updatedTicket.setResolvedAt(LocalDateTime.now());
        updatedTicket.setResolutionNotes("Issue resolved");

        when(ticketRepository.findById(testTicketId)).thenReturn(Optional.of(ticket));
        when(ticketRepository.save(any(Ticket.class))).thenReturn(updatedTicket);

        // When
        TicketResponse response = ticketService.updateTicketStatus(
                testTicketId, TicketStatus.RESOLVED, "Issue resolved");

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatus()).isEqualTo(TicketStatus.RESOLVED);
        assertThat(response.getResolutionNotes()).isEqualTo("Issue resolved");
        assertThat(response.getResolvedAt()).isNotNull();

        verify(ticketRepository).findById(testTicketId);
        verify(ticketRepository).save(any(Ticket.class));
        verify(eventPublisher).publishEvent(any());
    }

    private Ticket createTestTicket(TicketType type) {
        return Ticket.builder()
                .ticketId(testTicketId)
                .type(type)
                .status(TicketStatus.OPEN)
                .title("Test Ticket")
                .description("Test description")
                .userId(testUserId)
                .priority("MEDIUM")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
