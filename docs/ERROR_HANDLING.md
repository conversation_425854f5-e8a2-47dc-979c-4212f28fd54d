# Error Handling System

This document describes the comprehensive error handling system implemented in the LookForX Admin Panel.

## Overview

The error handling system provides:
- Consistent error display across all pages
- Automatic error parsing and user-friendly messages
- Retry functionality for recoverable errors
- Form validation error handling
- Global error boundary for unhandled errors
- Toast notifications for errors and success messages

## Architecture

### Core Components

1. **ErrorBoundary** - Catches unhandled React errors
2. **ErrorProvider** - Global error state management
3. **ErrorNotification** - Reusable error display component
4. **useErrorHandler** - Hook for error handling logic
5. **useErrorNotification** - Combined hook for errors and notifications

### Error Types

The system handles various error types:
- **API Errors** - Backend service errors
- **Network Errors** - Connection issues
- **Validation Errors** - Form validation failures
- **Authentication Errors** - 401/403 responses
- **JavaScript Errors** - Runtime exceptions

## Usage

### Basic Error Handling

```tsx
import { useErrorNotification } from '@/contexts/ErrorContext';

const MyComponent = () => {
  const { handleError, showSuccess } = useErrorNotification();

  const handleAction = async () => {
    try {
      await someApiCall();
      showSuccess('Operation completed successfully');
    } catch (error) {
      handleError(error, {
        customMessage: 'Failed to complete operation',
        showRetry: true,
        onRetry: handleAction
      });
    }
  };
};
```

### Form Error Handling

```tsx
import { useFormErrorHandler, FormErrorHandler } from '@/components/shared/FormErrorHandler';

const MyForm = () => {
  const { formError, fieldErrors, handleFormError, getFieldError } = useFormErrorHandler();

  const handleSubmit = async (data) => {
    try {
      await submitForm(data);
    } catch (error) {
      handleFormError(error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <FormErrorHandler error={formError} onRetry={handleSubmit} />
      
      <input 
        name="email"
        className={getFieldError('email') ? 'border-red-500' : ''}
      />
      {getFieldError('email') && (
        <div className="text-red-500 text-sm">{getFieldError('email')}</div>
      )}
    </form>
  );
};
```

### Error Notifications

```tsx
import { useErrorContext } from '@/contexts/ErrorContext';

const MyComponent = () => {
  const { showError, showWarning, showInfo, showSuccess } = useErrorContext();

  const handleAction = () => {
    showError('Something went wrong');
    showWarning('Please check your input');
    showInfo('Processing your request');
    showSuccess('Operation completed');
  };
};
```

## Error Response Format

The system expects backend errors in this format (matching lookforx-common ErrorResponse):

```json
{
  "status": 400,
  "error": "Bad Request",
  "message": "User-friendly error message",
  "errorCode": "VALIDATION_ERROR",
  "path": "/api/endpoint",
  "timestamp": "2024-01-01T12:00:00Z",
  "fieldErrors": {
    "email": "Email is required",
    "password": "Password must be at least 8 characters"
  },
  "traceId": "abc123"
}
```

## Configuration

### Error Provider Setup

The ErrorProvider is configured in the main layout:

```tsx
// app/layout.tsx
<ErrorBoundary>
  <ErrorProvider maxNotifications={5} defaultDuration={5000}>
    <YourApp />
  </ErrorProvider>
</ErrorBoundary>
```

### API Interceptor

The API client automatically logs errors and handles token refresh:

```tsx
// lib/api.ts
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 token refresh
    // Log errors for debugging
    return Promise.reject(error);
  }
);
```

## Error Handling Patterns

### 1. Service Layer Errors

Services should throw errors without handling them:

```tsx
// lib/category-service.ts
class CategoryService {
  async getAllCategories(): Promise<Category[]> {
    const response = await api.get<Category[]>('/categories');
    return response.data; // Let errors bubble up
  }
}
```

### 2. Component Error Handling

Components should handle errors with appropriate user feedback:

```tsx
const CategoryList = () => {
  const { handleError, showSuccess } = useErrorNotification();

  const loadCategories = async () => {
    try {
      const categories = await categoryService.getAllCategories();
      setCategories(categories);
    } catch (error) {
      handleError(error, {
        customMessage: 'Failed to load categories',
        showRetry: true,
        onRetry: loadCategories
      });
    }
  };
};
```

### 3. Form Validation

Use the form error handler for validation errors:

```tsx
const CategoryForm = () => {
  const { formError, handleFormError, getFieldError } = useFormErrorHandler();

  const handleSubmit = async (data) => {
    try {
      await categoryService.createCategory(data);
      showSuccess('Category created successfully');
    } catch (error) {
      handleFormError(error); // Automatically handles validation errors
    }
  };
};
```

## Best Practices

1. **Always provide user-friendly messages** - Don't show technical error details to users
2. **Use retry functionality** - For network and server errors
3. **Handle validation errors properly** - Show field-specific errors
4. **Log errors for debugging** - But don't expose sensitive information
5. **Provide context** - Include what operation failed
6. **Use consistent error types** - Follow the established patterns

## Error Codes

Common error codes used in the system:

- `VALIDATION_ERROR` - Form validation failures
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Access denied
- `NOT_FOUND` - Resource not found
- `CONFLICT` - Data conflict (e.g., duplicate entries)
- `INTERNAL_SERVER_ERROR` - Server errors
- `NETWORK_ERROR` - Connection issues

## Testing Error Handling

Test error scenarios by:

1. **Network errors** - Disconnect internet
2. **Server errors** - Stop backend services
3. **Validation errors** - Submit invalid form data
4. **Authentication errors** - Use expired tokens
5. **JavaScript errors** - Trigger runtime exceptions

## Troubleshooting

Common issues and solutions:

1. **Errors not showing** - Check if ErrorProvider is properly configured
2. **Duplicate error messages** - Ensure only one error handler per operation
3. **Errors not clearing** - Call clearErrors() when appropriate
4. **Network errors not retrying** - Check retry logic implementation
